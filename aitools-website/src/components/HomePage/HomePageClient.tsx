'use client';
import React, { useState } from 'react';
import ToolCard from '@/components/ToolCard';
import LoginModal from '@/components/auth/LoginModal';
import { Tool } from '@/lib/api';

interface HomePageClientProps {
  featuredTools: Tool[];
  todayTools: Tool[];
  recentTools: Tool[];
}

export default function HomePageClient({ featuredTools, todayTools, recentTools }: HomePageClientProps) {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  return (
    <>
      {/* Today's Tools Section */}
      {todayTools.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50" aria-labelledby="today-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="today-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-green-600" aria-hidden="true">📅</span>
                今日发布
              </h2>
              <p className="text-lg text-gray-600">
                今天刚刚发布的最新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list" aria-label="今日发布的AI工具">
              {todayTools.map((tool) => (
                <article key={tool._id} role="listitem">
                  <ToolCard
                    tool={tool}
                    onLoginRequired={() => setIsLoginModalOpen(true)}
                  />
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Tools Section */}
      {recentTools.length > 0 && (
        <section className="py-16 bg-white" aria-labelledby="recent-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="recent-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 text-blue-600" aria-hidden="true">🕒</span>
                最近发布
              </h2>
              <p className="text-lg text-gray-600">
                过去一周内发布的新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentTools.map((tool) => (
                <ToolCard
                  key={tool._id}
                  tool={tool}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
              ))}
            </div>

            <div className="text-center mt-8">
              <a
                href="/tools?sort=publishedAt&order=desc"
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
              >
                查看更多最新工具
              </a>
            </div>
          </div>
        </section>
      )}

      {/* Featured Tools Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <span className="inline-block mr-2 text-blue-600" aria-hidden="true">📈</span>
              热门工具
            </h2>
            <p className="text-lg text-gray-600">
              最受欢迎和评价最高的 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTools.map((tool) => (
              <ToolCard
                key={tool._id}
                tool={tool}
                onLoginRequired={() => setIsLoginModalOpen(true)}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <a
              href="/tools"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看更多工具
            </a>
          </div>
        </div>
      </section>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
}
