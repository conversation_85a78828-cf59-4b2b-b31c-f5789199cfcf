'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import ToolCard from '@/components/ToolCard';
import CategoryCard from '@/components/CategoryCard';
import LoginModal from '@/components/auth/LoginModal';
import { Tool } from '@/lib/api';

interface Category {
  id: string;
  name: string;
  count: number;
  totalViews: number;
  totalLikes: number;
}

interface ClientHomePageProps {
  featuredTools: Tool[];
  todayTools: Tool[];
  recentTools: Tool[];
  categories: Category[];
}

export default function ClientHomePage({ 
  featuredTools, 
  todayTools, 
  recentTools, 
  categories 
}: ClientHomePageProps) {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  // 转换分类数据格式以匹配 CategoryCard 组件的期望
  const formattedCategories = categories.map(category => ({
    _id: category.id,
    name: category.name,
    slug: category.id,
    description: `AI tools for ${category.name.toLowerCase()}`,
    icon: getCategoryIcon(category.id),
    color: getCategoryColor(category.id),
    toolCount: category.count
  }));

  return (
    <>
      {/* Today's Tools Section */}
      {todayTools.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50" aria-labelledby="today-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="today-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 h-8 w-8 text-green-600" aria-hidden="true">📅</span>
                今日发布
              </h2>
              <p className="text-lg text-gray-600">
                今天刚刚发布的最新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" role="list" aria-label="今日发布的AI工具">
              {todayTools.map((tool) => (
                <article key={tool._id} role="listitem">
                  <ToolCard
                    tool={tool}
                    onLoginRequired={() => setIsLoginModalOpen(true)}
                  />
                </article>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Recent Tools Section */}
      {recentTools.length > 0 && (
        <section className="py-16 bg-white" aria-labelledby="recent-tools-heading">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <header className="text-center mb-12">
              <h2 id="recent-tools-heading" className="text-3xl font-bold text-gray-900 mb-4">
                <span className="inline-block mr-2 h-8 w-8 text-blue-600" aria-hidden="true">🕒</span>
                最近发布
              </h2>
              <p className="text-lg text-gray-600">
                过去一周内发布的新 AI 工具
              </p>
            </header>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentTools.map((tool) => (
                <ToolCard
                  key={tool._id}
                  tool={tool}
                  onLoginRequired={() => setIsLoginModalOpen(true)}
                />
              ))}
            </div>

            <div className="text-center mt-8">
              <Link
                href="/tools?sort=publishedAt&order=desc"
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
              >
                查看更多最新工具
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Featured Tools Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <span className="inline-block mr-2 h-8 w-8 text-blue-600">📈</span>
              热门工具
            </h2>
            <p className="text-lg text-gray-600">
              最受欢迎和评价最高的 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTools.map((tool) => (
              <ToolCard
                key={tool._id}
                tool={tool}
                onLoginRequired={() => setIsLoginModalOpen(true)}
              />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/tools"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看更多工具
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              <span className="inline-block mr-2 h-8 w-8 text-blue-600">⭐</span>
              热门分类
            </h2>
            <p className="text-lg text-gray-600">
              按功能分类浏览 AI 工具
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {formattedCategories.map((category) => (
              <CategoryCard key={category._id} category={category} />
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/categories"
              className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors"
            >
              查看所有分类
            </Link>
          </div>
        </div>
      </section>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
}

// 辅助函数：根据分类ID获取图标
function getCategoryIcon(categoryId: string): string {
  const iconMap: Record<string, string> = {
    'text-generation': '📝',
    'image-generation': '🎨',
    'code-generation': '💻',
    'data-analysis': '📊',
    'audio-processing': '🎵',
    'video-editing': '🎬',
    'translation': '🌐',
    'search-engines': '🔍',
    'education': '📚',
    'marketing': '📢',
    'productivity': '⚡',
    'customer-service': '🎧'
  };
  return iconMap[categoryId] || '🔧';
}

// 辅助函数：根据分类ID获取颜色
function getCategoryColor(categoryId: string): string {
  const colorMap: Record<string, string> = {
    'text-generation': '#3B82F6',
    'image-generation': '#8B5CF6',
    'code-generation': '#F59E0B',
    'data-analysis': '#06B6D4',
    'audio-processing': '#10B981',
    'video-editing': '#F97316',
    'translation': '#6366F1',
    'search-engines': '#84CC16',
    'education': '#EC4899',
    'marketing': '#EF4444',
    'productivity': '#8B5CF6',
    'customer-service': '#14B8A6'
  };
  return colorMap[categoryId] || '#6B7280';
}
