import React, { useState } from 'react';
import { GetStaticProps } from 'next';
import Layout from '@/components/Layout';
import ToolCard from '@/components/ToolCard';
import { Tool } from '@/lib/api';
import { TOOL_PRICING_OPTIONS } from '@/constants/pricing';
import { Search, Filter, Grid, List, ChevronDown } from 'lucide-react';
import dbConnect from '@/lib/mongodb';
import ToolModel from '@/models/Tool';

// 工具目录页面组件

const categories = [
  { value: '', label: '所有分类' },
  { value: 'text-generation', label: '文本生成' },
  { value: 'image-generation', label: '图像生成' },
  { value: 'code-generation', label: '代码生成' },
  { value: 'data-analysis', label: '数据分析' },
  { value: 'audio-processing', label: '音频处理' },
  { value: 'video-editing', label: '视频编辑' }
];

// 使用统一的价格选项配置
const pricingOptions = TOOL_PRICING_OPTIONS;

const sortOptions = [
  { value: 'popular', label: '最受欢迎' },
  { value: 'newest', label: '最新添加' },
  { value: 'name', label: '名称排序' },
  { value: 'views', label: '浏览量' }
];

interface ToolsPageProps {
  tools: Tool[];
  totalCount: number;
}

export default function ToolsPage({ tools, totalCount }: ToolsPageProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedPricing, setSelectedPricing] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // Filter and sort tools
  const filteredTools = tools.filter((tool: Tool) => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = !selectedCategory || tool.category === selectedCategory;
    const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;

    return matchesSearch && matchesCategory && matchesPricing;
  });

  const sortedTools = [...filteredTools].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return (b.likes || 0) - (a.likes || 0);
      case 'views':
        return (b.views || 0) - (a.views || 0);
      case 'name':
        return a.name.localeCompare(b.name);
      case 'newest':
        return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();
      default:
        return 0;
    }
  });

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">AI 工具目录</h1>
          <p className="text-lg text-gray-600">
            发现 {totalCount} 个精选的 AI 工具，提升您的工作效率
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          {/* Search Bar */}
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="搜索工具名称、描述或标签..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
          </div>

          {/* Filter Toggle Button (Mobile) */}
          <div className="md:hidden mb-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              <Filter className="mr-2 h-4 w-4" />
              筛选选项
              <ChevronDown className={`ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {/* Filters */}
          <div className={`grid grid-cols-1 md:grid-cols-4 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`}>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">分类</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">价格</label>
              <select
                value={selectedPricing}
                onChange={(e) => setSelectedPricing(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {pricingOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">排序</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">视图</label>
              <div className="flex rounded-lg border border-gray-300">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${
                    viewMode === 'grid'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Grid className="h-4 w-4 mx-auto" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${
                    viewMode === 'list'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <List className="h-4 w-4 mx-auto" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-gray-600">
            显示 {sortedTools.length} 个结果
            {searchTerm && ` 搜索 "${searchTerm}"`}
            {selectedCategory && ` 在 "${categories.find(c => c.value === selectedCategory)?.label}"`}
          </p>
        </div>

        {/* Tools Grid/List */}
        {sortedTools.length > 0 ? (
          <div className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
          }>
            {sortedTools.map((tool) => (
              <ToolCard key={tool._id} tool={tool} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的工具</h3>
            <p className="text-gray-600">
              尝试调整搜索条件或筛选选项
            </p>
          </div>
        )}

        {/* Pagination would go here in a real application */}
      </div>
    </Layout>
  );
}

// 服务端数据获取函数
export const getStaticProps: GetStaticProps<ToolsPageProps> = async () => {
  try {
    await dbConnect();

    // 获取所有已发布的工具
    const tools = await ToolModel.find({ status: 'published' })
      .sort({ views: -1, likes: -1, publishedAt: -1 })
      .select('-submittedBy -reviewNotes -reviewedBy')
      .lean();

    // 获取工具总数
    const totalCount = await ToolModel.countDocuments({ status: 'published' });

    // 序列化数据以避免 Next.js 序列化问题
    const serializedData = {
      tools: JSON.parse(JSON.stringify(tools)),
      totalCount
    };

    return {
      props: serializedData,
      // 重新验证时间：10分钟
      revalidate: 600
    };

  } catch (error) {
    console.error('Error fetching tools data:', error);

    // 返回空数据而不是失败，确保页面能够渲染
    return {
      props: {
        tools: [],
        totalCount: 0
      },
      // 出错时更短的重新验证时间
      revalidate: 60
    };
  }
};
