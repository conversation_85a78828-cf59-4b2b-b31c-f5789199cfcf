(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var l=t(5155),a=t(2115),r=t(2108),i=t(9911);function n(e){let{isOpen:s,onClose:t}=e,[n,c]=(0,a.useState)("method"),[d,o]=(0,a.useState)(""),[x,m]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[u,b]=(0,a.useState)(""),p=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},y=()=>{c("method"),o(""),m(""),b(""),t()},j=async e=>{try{g(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{g(!1)}},f=async()=>{if(!d)return void b("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d))return void b("请输入有效的邮箱地址");b(""),g(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d})}),s=await e.json();s.success?(m(s.token),c("code"),p("验证码已发送，请查看您的邮箱")):p(s.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{g(!1)}},N=async e=>{if(6===e.length){g(!0);try{let s=await (0,r.signIn)("email-code",{email:d,code:e,token:x,redirect:!1});(null==s?void 0:s.ok)?(p("登录成功，欢迎回来！"),y()):p((null==s?void 0:s.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{g(!1)}}},v=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var l;null==(l=t[e+1])||l.focus()}let a=Array.from(t).map(e=>e.value).join("");6===a.length&&N(a)};return s?(0,l.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:y}),(0,l.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===n&&"登录 AI Tools Directory","email"===n&&"邮箱登录","code"===n&&"输入验证码"]}),(0,l.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,l.jsx)(i.QCr,{})})]}),(0,l.jsxs)("div",{className:"p-6",children:["method"===n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>j("google"),disabled:h,children:[(0,l.jsx)(i.DSS,{}),"使用 Google 登录"]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>j("github"),disabled:h,children:[(0,l.jsx)(i.hL4,{}),"使用 GitHub 登录"]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,l.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,l.jsx)(i.maD,{}),"使用邮箱登录"]})]}),"email"===n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,l.jsx)("input",{type:"email",value:d,onChange:e=>o(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&f(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),u&&(0,l.jsx)("p",{className:"mt-1 text-sm text-red-600",children:u})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:f,disabled:h,children:h?"发送中...":"发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",d," 的6位验证码"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,l.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,l.jsx)("input",{type:"text",maxLength:1,onChange:s=>v(e,s.target.value),disabled:h,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,l.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},6678:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,7070)),Promise.resolve().then(t.bind(t,365))},7070:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var l=t(5155),a=t(2115),r=t(7797),i=t(6063);function n(e){let{featuredTools:s,todayTools:t,recentTools:n}=e,[c,d]=(0,a.useState)(!1);return(0,l.jsxs)(l.Fragment,{children:[s.length>0&&(0,l.jsx)("section",{className:"py-16 bg-white","aria-labelledby":"featured-tools-heading",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("header",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{id:"featured-tools-heading",className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)("span",{className:"inline-block mr-2 text-yellow-500","aria-hidden":"true",children:"⭐"}),"热门推荐"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"最受欢迎的 AI 工具推荐"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",role:"list","aria-label":"热门推荐的AI工具",children:s.map(e=>(0,l.jsx)("article",{role:"listitem",children:(0,l.jsx)(r.A,{tool:e,onLoginRequired:()=>d(!0)})},e._id))})]})}),t.length>0&&(0,l.jsx)("section",{className:"py-16 bg-gradient-to-r from-green-50 to-blue-50","aria-labelledby":"today-tools-heading",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("header",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{id:"today-tools-heading",className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)("span",{className:"inline-block mr-2 text-green-600","aria-hidden":"true",children:"\uD83D\uDCC5"}),"今日发布"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"今天刚刚发布的最新 AI 工具"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",role:"list","aria-label":"今日发布的AI工具",children:t.map(e=>(0,l.jsx)("article",{role:"listitem",children:(0,l.jsx)(r.A,{tool:e,onLoginRequired:()=>d(!0)})},e._id))})]})}),n.length>0&&(0,l.jsx)("section",{className:"py-16 bg-white","aria-labelledby":"recent-tools-heading",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("header",{className:"text-center mb-12",children:[(0,l.jsxs)("h2",{id:"recent-tools-heading",className:"text-3xl font-bold text-gray-900 mb-4",children:[(0,l.jsx)("span",{className:"inline-block mr-2 text-blue-600","aria-hidden":"true",children:"\uD83D\uDD52"}),"最近发布"]}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"过去一周发布的热门 AI 工具"})]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",role:"list","aria-label":"最近发布的AI工具",children:n.map(e=>(0,l.jsx)("article",{role:"listitem",children:(0,l.jsx)(r.A,{tool:e,onLoginRequired:()=>d(!0)})},e._id))})]})}),(0,l.jsx)(i.A,{isOpen:c,onClose:()=>d(!1)})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>s(6678)),_N_E=e.O()}]);