(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{347:()=>{},848:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(5155),l=r(2108);function a(e){let{children:t}=e;return(0,s.jsx)(l<PERSON>,{children:t})}},3669:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(5155),l=r(2115),a=r(6874),n=r.n(a),i=r(5695),c=r(9911),o=r(2108),d=r(6063);function u(){var e,t,r,a,n,u,m,x,h,b,f;let{data:p,status:g}=(0,o.useSession)(),y=(0,i.useRouter)(),[j,v]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),k=async()=>{await (0,o.signOut)({callbackUrl:"/"})},O=e=>{w(!1),y.push(e)};return"loading"===g?(0,s.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:"加载中..."}):p?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>w(!N),children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=p.user)?void 0:e.image)?(0,s.jsx)("img",{src:p.user.image,alt:p.user.name||"",className:"w-full h-full object-cover"}):(0,s.jsx)("span",{className:"text-sm font-medium text-gray-600",children:(null==(r=p.user)||null==(t=r.name)?void 0:t.charAt(0))||"U"})}),(0,s.jsx)("span",{className:"text-sm hidden md:block",children:null==(a=p.user)?void 0:a.name}),(0,s.jsx)(c.Vr3,{className:"text-xs transition-transform ".concat(N?"rotate-180":"")})]}),N&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>w(!1)}),(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",children:[(0,s.jsx)("div",{className:"p-4 border-b",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(n=p.user)?void 0:n.image)?(0,s.jsx)("img",{src:p.user.image,alt:p.user.name||"",className:"w-full h-full object-cover"}):(0,s.jsx)("span",{className:"text-lg font-medium text-gray-600",children:(null==(m=p.user)||null==(u=m.name)?void 0:u.charAt(0))||"U"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-sm",children:null==(x=p.user)?void 0:x.name}),(0,s.jsx)("p",{className:"text-gray-500 text-xs",children:null==(h=p.user)?void 0:h.email}),(null==(b=p.user)?void 0:b.role)==="admin"&&(0,s.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"})]})]})}),(0,s.jsxs)("div",{className:"py-2",children:[(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>O("/profile"),children:[(0,s.jsx)(c.x$1,{}),"个人资料"]}),(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>O("/profile/submitted"),children:[(0,s.jsx)(c.svy,{}),"我提交的工具"]}),(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>O("/profile/liked"),children:[(0,s.jsx)(c.Mbv,{}),"我的收藏"]}),(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>O("/submit"),children:[(0,s.jsx)(c.OiG,{}),"提交工具"]})]}),(null==(f=p.user)?void 0:f.role)==="admin"&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"border-t py-2",children:(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>O("/admin"),children:[(0,s.jsx)(c.Pcn,{}),"管理后台"]})})}),(0,s.jsxs)("div",{className:"border-t py-2",children:[(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>O("/settings"),children:[(0,s.jsx)(c.Pcn,{}),"设置"]}),(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",onClick:k,children:[(0,s.jsx)(c.axc,{}),"退出登录"]})]})]})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>v(!0),children:[(0,s.jsx)(c.Zu,{}),"登录"]}),(0,s.jsx)(d.A,{isOpen:j,onClose:()=>v(!1)})]})}let m=e=>{let{children:t,href:r}=e;return(0,s.jsx)(n(),{href:r,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:t})},x=[{name:"首页",href:"/"},{name:"工具目录",href:"/tools"},{name:"分类",href:"/categories"},{name:"提交工具",href:"/submit"}];function h(){let[e,t]=(0,l.useState)(!1),r=(0,i.useRouter)(),a=e=>{e.preventDefault();let t=new FormData(e.currentTarget).get("search");t.trim()&&r.push("/search?q=".concat(encodeURIComponent(t.trim())))};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,s.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 hover:no-underline",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,s.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,s.jsx)("nav",{className:"hidden md:flex space-x-4",children:x.map(e=>(0,s.jsx)(m,{href:e.href,children:e.name},e.name))})]}),(0,s.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,s.jsx)("form",{onSubmit:a,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(c.KSO,{className:"text-gray-400"})}),(0,s.jsx)("input",{name:"search",type:"text",placeholder:"搜索 AI 工具...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u,{}),(0,s.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>t(!e),"aria-label":"Open Menu",children:e?(0,s.jsx)(c.QCr,{}):(0,s.jsx)(c.OXb,{})})]})]}),e&&(0,s.jsx)("div",{className:"md:hidden pb-4",children:(0,s.jsxs)("nav",{className:"space-y-4",children:[x.map(e=>(0,s.jsx)(m,{href:e.href,children:e.name},e.name)),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)("form",{onSubmit:a,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(c.KSO,{className:"text-gray-400"})}),(0,s.jsx)("input",{name:"search",type:"text",placeholder:"搜索 AI 工具...",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})})]})})]})})}},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>d});var s=r(2115),l={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=s.createContext&&s.createContext(l),n=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var s,l,a;s=e,l=t,a=r[t],(l=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(l))in s?Object.defineProperty(s,l,{value:a,enumerable:!0,configurable:!0,writable:!0}):s[l]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e){return t=>s.createElement(u,i({attr:o({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,o({key:r},t.attr),e(t.child)))}(e.child))}function u(e){var t=t=>{var r,{attr:l,size:a,title:c}=e,d=function(e,t){if(null==e)return{};var r,s,l=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(s=0;s<a.length;s++)r=a[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(l[r]=e[r])}return l}(e,n),u=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,l,d,{className:r,style:o(o({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&s.createElement("title",null,c),e.children)};return void 0!==a?s.createElement(a.Consumer,null,e=>t(e)):t(l)}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},6063:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(5155),l=r(2115),a=r(2108),n=r(9911);function i(e){let{isOpen:t,onClose:r}=e,[i,c]=(0,l.useState)("method"),[o,d]=(0,l.useState)(""),[u,m]=(0,l.useState)(""),[x,h]=(0,l.useState)(!1),[b,f]=(0,l.useState)(""),p=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",r=document.createElement("div");r.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),r.textContent=e,document.body.appendChild(r),setTimeout(()=>document.body.removeChild(r),3e3)},g=()=>{c("method"),d(""),m(""),f(""),r()},y=async e=>{try{h(!0),await (0,a.signIn)(e,{callbackUrl:"/"})}catch(e){p("登录失败，请稍后重试","error")}finally{h(!1)}},j=async()=>{if(!o)return void f("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return void f("请输入有效的邮箱地址");f(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),t=await e.json();t.success?(m(t.token),c("code"),p("验证码已发送，请查看您的邮箱")):p(t.error||"发送失败，请稍后重试","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}},v=async e=>{if(6===e.length){h(!0);try{let t=await (0,a.signIn)("email-code",{email:o,code:e,token:u,redirect:!1});(null==t?void 0:t.ok)?(p("登录成功，欢迎回来！"),g()):p((null==t?void 0:t.error)||"验证码错误","error")}catch(e){p("网络错误，请检查网络连接","error")}finally{h(!1)}}},N=(e,t)=>{if(t.length>1)return;let r=document.querySelectorAll(".code-input");if(r[e].value=t,t&&e<5){var s;null==(s=r[e+1])||s.focus()}let l=Array.from(r).map(e=>e.value).join("");6===l.length&&v(l)};return t?(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:g}),(0,s.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,s.jsx)("button",{onClick:g,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(n.QCr,{})})]}),(0,s.jsxs)("div",{className:"p-6",children:["method"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>y("google"),disabled:x,children:[(0,s.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,s.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>y("github"),disabled:x,children:[(0,s.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,s.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,s.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,s.jsx)("input",{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&j(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),b&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:j,disabled:x,children:x?"发送中...":"发送验证码"}),(0,s.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===i&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",o," 的6位验证码"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,s.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,s.jsx)("input",{type:"text",maxLength:1,onChange:t=>N(e,t.target.value),disabled:x,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,s.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},7162:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4147,23)),Promise.resolve().then(r.t.bind(r,8489,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,3669)),Promise.resolve().then(r.bind(r,848))},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}}},e=>{var t=t=>e(e.s=t);e.O(0,[7896,6711,6874,2108,8441,1684,7358],()=>t(7162)),_N_E=e.O()}]);