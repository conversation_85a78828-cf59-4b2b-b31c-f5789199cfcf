(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{1976:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},4309:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(5155),r=t(2115),l=t(2108),i=t(5695),d=t(6874),c=t.n(d),n=t(4478),o=t(2731),m=t(5731);let x=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var h=t(4616),u=t(2713),g=t(2657),j=t(1976),p=t(9074);function N(){var e,s,t,d,N,f;let{data:b,status:y}=(0,l.useSession)(),v=(0,i.useRouter)(),[w,A]=(0,r.useState)({submittedTools:0,approvedTools:0,totalViews:0,totalLikes:0,likedTools:0}),[S,T]=(0,r.useState)([]),[k,P]=(0,r.useState)(!0);(0,r.useEffect)(()=>{if("unauthenticated"===y)return void v.push("/");"authenticated"===y&&E()},[y,v]);let E=async()=>{try{P(!0);let e=await m.u.getAdminTools();if(e.success&&e.data){let s=e.data.tools,t=s.filter(e=>"approved"===e.status);A({submittedTools:s.length,approvedTools:t.length,totalViews:t.reduce((e,s)=>e+s.views,0),totalLikes:t.reduce((e,s)=>e+s.likes,0),likedTools:0}),T(s.slice(0,3))}}catch(e){console.error("Error fetching user data:",e)}finally{P(!1)}};return"loading"===y||k?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(o.A,{size:"lg",className:"py-20"})})}):b?(0,a.jsx)(n.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:(null==(e=b.user)?void 0:e.image)?(0,a.jsx)("img",{src:b.user.image,alt:b.user.name||"",className:"w-full h-full object-cover"}):(0,a.jsx)("span",{className:"text-2xl font-medium text-gray-600",children:(null==(t=b.user)||null==(s=t.name)?void 0:s.charAt(0))||"U"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:null==(d=b.user)?void 0:d.name}),(0,a.jsx)("p",{className:"text-gray-600",children:null==(N=b.user)?void 0:N.email}),(null==(f=b.user)?void 0:f.role)==="admin"&&(0,a.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["加入时间：",new Date().toLocaleDateString("zh-CN")]})]})]}),(0,a.jsxs)(c(),{href:"/settings",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[(0,a.jsx)(x,{className:"mr-2 h-4 w-4"}),"编辑资料"]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"提交工具"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.submittedTools})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.approvedTools})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(g.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.totalViews})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(j.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"获得点赞"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:w.totalLikes})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)(c(),{href:"/profile/submitted",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"我提交的工具"}),(0,a.jsx)("p",{className:"text-gray-600",children:"管理您提交的AI工具"})]})]})}),(0,a.jsx)(c(),{href:"/profile/liked",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-red-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"我的收藏"}),(0,a.jsx)("p",{className:"text-gray-600",children:"查看您收藏的工具"})]})]})}),(0,a.jsx)(c(),{href:"/submit",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"提交新工具"}),(0,a.jsx)("p",{className:"text-gray-600",children:"分享您发现的AI工具"})]})]})})]}),S.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"最近提交的工具"}),(0,a.jsx)(c(),{href:"/profile/submitted",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"查看全部"})]}),(0,a.jsx)("div",{className:"space-y-4",children:S.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 mr-1"}),new Date(e.submittedAt).toLocaleDateString("zh-CN")]}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("approved"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:"approved"===e.status?"已通过":"pending"===e.status?"审核中":"已拒绝"})]})]}),"approved"===e.status&&(0,a.jsx)(c(),{href:"/tools/".concat(e._id),className:"ml-4 text-blue-600 hover:text-blue-700",children:(0,a.jsx)(g.A,{className:"h-5 w-5"})})]},e._id))})]})]})}):null}},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},5731:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let a=t(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class r{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...s.headers},...s},r=await fetch(t,a),l=await r.json();if(!r.ok)throw Error(l.error||"HTTP error! status: ".concat(r.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/user/liked-tools".concat(t?"?".concat(t):""))}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=a){this.baseURL=e}}let l=new r},6991:(e,s,t)=>{Promise.resolve().then(t.bind(t,4309))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,2108,9534,8441,1684,7358],()=>s(6991)),_N_E=e.O()}]);