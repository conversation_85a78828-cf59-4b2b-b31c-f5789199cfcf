(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3554],{800:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var r=s(5155),a=s(2115),l=s(7797),n=s(2731),c=s(9783),o=s(6063),i=s(5731),d=s(3467),u=s(7924),x=s(4653),h=s(5968);let m=[{value:"",label:"所有分类"},{value:"text-generation",label:"文本生成"},{value:"image-generation",label:"图像生成"},{value:"code-generation",label:"代码生成"},{value:"data-analysis",label:"数据分析"},{value:"audio-processing",label:"音频处理"},{value:"video-editing",label:"视频编辑"}],g=d.v4,p=[{value:"popular",label:"最受欢迎"},{value:"newest",label:"最新添加"},{value:"name",label:"名称排序"},{value:"views",label:"浏览量"}];function b(e){let{initialTools:t,totalCount:s}=e,[d,b]=(0,a.useState)(t),[y,v]=(0,a.useState)(!1),[j,f]=(0,a.useState)(""),[N,k]=(0,a.useState)(""),[w,S]=(0,a.useState)(""),[A,C]=(0,a.useState)(""),[T,q]=(0,a.useState)("popular"),[E,P]=(0,a.useState)("grid"),[O,L]=(0,a.useState)(!1),[M,_]=(0,a.useState)(!1);(0,a.useEffect)(()=>{(N||w||A||"popular"!==T)&&U()},[N,w,A,T]);let U=async()=>{try{v(!0),f("");let e={status:"published",limit:50};switch(N&&(e.search=N),w&&(e.category=w),A&&(e.pricing=A),T){case"newest":e.sort="publishedAt",e.order="desc";break;case"name":e.sort="name",e.order="asc";break;case"views":e.sort="views",e.order="desc";break;default:e.sort="likes",e.order="desc"}let t=await i.u.getTools(e);t.success&&t.data?b(t.data.tools):f("获取工具列表失败")}catch(e){console.error("Error fetching tools:",e),f("网络错误，请重试")}finally{v(!1)}},z=()=>{k(""),S(""),C(""),q("popular"),b(t)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:[(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),U()},className:"mb-4",children:(0,r.jsxs)("div",{className:"relative max-w-2xl mx-auto",children:[(0,r.jsx)("input",{type:"text",placeholder:"搜索 AI 工具...",value:N,onChange:e=>k(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,r.jsx)(u.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("select",{value:w,onChange:e=>S(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:m.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,r.jsxs)("select",{value:A,onChange:e=>C(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"所有价格"}),g.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,r.jsx)("select",{value:T,onChange:e=>q(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:p.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,r.jsx)("button",{type:"button",onClick:z,className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50",children:"重置"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:()=>P("grid"),className:"p-2 rounded-md ".concat("grid"===E?"bg-blue-100 text-blue-600":"text-gray-400 hover:text-gray-600"),children:(0,r.jsx)(x.A,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>P("list"),className:"p-2 rounded-md ".concat("list"===E?"bg-blue-100 text-blue-600":"text-gray-400 hover:text-gray-600"),children:(0,r.jsx)(h.A,{className:"h-5 w-5"})})]})]})]})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-600",children:["找到 ",(0,r.jsx)("span",{className:"font-semibold",children:d.length})," 个工具",s>d.length&&(0,r.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(共 ",s," 个)"]})]})}),j&&(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(c.A,{message:j,onClose:()=>f("")})}),y&&(0,r.jsx)("div",{className:"flex justify-center py-12",children:(0,r.jsx)(n.A,{size:"lg"})}),!y&&(0,r.jsx)("div",{className:"grid"===E?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:d.map(e=>(0,r.jsx)(l.A,{tool:e,onLoginRequired:()=>_(!0)},e._id))}),!y&&0===d.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(u.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"没有找到匹配的工具"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"尝试调整搜索条件或筛选器"}),(0,r.jsx)("button",{onClick:z,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"重置筛选器"})]})]}),(0,r.jsx)(o.A,{isOpen:M,onClose:()=>_(!1)})]})}},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5155);function a(e){let{size:t="md",className:s=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4653:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},4712:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.bind(s,365)),Promise.resolve().then(s.bind(s,800))},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5731:(e,t,s)=>{"use strict";s.d(t,{u:()=>l});let r=s(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=r){this.baseURL=e}}let l=new a},5968:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6063:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(5155),a=s(2115),l=s(2108),n=s(9911);function c(e){let{isOpen:t,onClose:s}=e,[c,o]=(0,a.useState)("method"),[i,d]=(0,a.useState)(""),[u,x]=(0,a.useState)(""),[h,m]=(0,a.useState)(!1),[g,p]=(0,a.useState)(""),b=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",s=document.createElement("div");s.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},y=()=>{o("method"),d(""),x(""),p(""),s()},v=async e=>{try{m(!0),await (0,l.signIn)(e,{callbackUrl:"/"})}catch(e){b("登录失败，请稍后重试","error")}finally{m(!1)}},j=async()=>{if(!i)return void p("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i))return void p("请输入有效的邮箱地址");p(""),m(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:i})}),t=await e.json();t.success?(x(t.token),o("code"),b("验证码已发送，请查看您的邮箱")):b(t.error||"发送失败，请稍后重试","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{m(!1)}},f=async e=>{if(6===e.length){m(!0);try{let t=await (0,l.signIn)("email-code",{email:i,code:e,token:u,redirect:!1});(null==t?void 0:t.ok)?(b("登录成功，欢迎回来！"),y()):b((null==t?void 0:t.error)||"验证码错误","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{m(!1)}}},N=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");if(s[e].value=t,t&&e<5){var r;null==(r=s[e+1])||r.focus()}let a=Array.from(s).map(e=>e.value).join("");6===a.length&&f(a)};return t?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:y}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===c&&"登录 AI Tools Directory","email"===c&&"邮箱登录","code"===c&&"输入验证码"]}),(0,r.jsx)("button",{onClick:y,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===c&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>v("google"),disabled:h,children:[(0,r.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>v("github"),disabled:h,children:[(0,r.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>o("email"),children:[(0,r.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===c&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,r.jsx)("input",{type:"email",value:i,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&j(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:j,disabled:h,children:h?"发送中...":"发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("method"),children:"返回"})]})]}),"code"===c&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",i," 的6位验证码"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:t=>N(e,t.target.value),disabled:h,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("email"),children:"重新发送验证码"}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>o("method"),children:"返回"})]})]})]})]})]}):null}},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9783:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(5155),a=s(5339),l=s(4416);function n(e){let{message:t,onClose:s,className:n=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:t})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>t(4712)),_N_E=e.O()}]);