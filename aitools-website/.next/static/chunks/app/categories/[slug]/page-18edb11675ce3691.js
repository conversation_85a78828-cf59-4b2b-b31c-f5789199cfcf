(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5093],{1987:(e,a,l)=>{Promise.resolve().then(l.t.bind(l,6874,23)),Promise.resolve().then(l.bind(l,2291)),Promise.resolve().then(l.bind(l,365))},2291:(e,a,l)=>{"use strict";l.d(a,{default:()=>h});var t=l(5155),s=l(2115),r=l(7797),d=l(7924),n=l(6932);let i=(0,l(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var c=l(4653),o=l(5968);let u=[{value:"",label:"所有价格"},{value:"free",label:"免费"},{value:"freemium",label:"免费增值"},{value:"paid",label:"付费"}],m=[{value:"newest",label:"最新发布"},{value:"popular",label:"最受欢迎"},{value:"name",label:"名称排序"}];function h(e){var a;let{category:l,initialTools:h,totalCount:x}=e,[g,b]=(0,s.useState)(""),[p,v]=(0,s.useState)(""),[y,f]=(0,s.useState)("newest"),[j,k]=(0,s.useState)("grid"),[w,N]=(0,s.useState)(!1),A=(0,s.useMemo)(()=>{let e=h;if(g){let a=g.toLowerCase();e=e.filter(e=>e.name.toLowerCase().includes(a)||e.description.toLowerCase().includes(a)||e.tags.some(e=>e.toLowerCase().includes(a)))}return p&&(e=e.filter(e=>e.pricing===p)),e.sort((e,a)=>{switch(y){case"newest":return new Date(a.createdAt).getTime()-new Date(e.createdAt).getTime();case"popular":var l,t;return((null==(l=a.likedBy)?void 0:l.length)||0)-((null==(t=e.likedBy)?void 0:t.length)||0);case"name":return e.name.localeCompare(a.name);default:return 0}}),e},[h,g,p,y]);return(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,t.jsxs)("div",{className:"relative mb-4",children:[(0,t.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或标签...",value:g,onChange:e=>b(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,t.jsx)(d.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"})]}),(0,t.jsx)("div",{className:"md:hidden mb-4",children:(0,t.jsxs)("button",{onClick:()=>N(!w),className:"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"筛选选项",(0,t.jsx)(i,{className:"ml-2 h-4 w-4 transform transition-transform ".concat(w?"rotate-180":"")})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 ".concat(w?"block":"hidden md:grid"),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格"}),(0,t.jsx)("select",{value:p,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:u.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"排序"}),(0,t.jsx)("select",{value:y,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:m.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"视图"}),(0,t.jsxs)("div",{className:"flex rounded-lg border border-gray-300",children:[(0,t.jsx)("button",{onClick:()=>k("grid"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ".concat("grid"===j?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,t.jsx)(c.A,{className:"h-4 w-4 mx-auto"})}),(0,t.jsx)("button",{onClick:()=>k("list"),className:"flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ".concat("list"===j?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-50"),children:(0,t.jsx)(o.A,{className:"h-4 w-4 mx-auto"})})]})]})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["显示 ",A.length," 个结果",g&&' 搜索 "'.concat(g,'"'),p&&" 价格: ".concat(null==(a=u.find(e=>e.value===p))?void 0:a.label)]})}),A.length>0?(0,t.jsx)("div",{className:"grid"===j?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:A.map(e=>(0,t.jsx)(r.A,{tool:e},e._id))}):(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 mb-4",children:(0,t.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"未找到匹配的工具"}),(0,t.jsx)("p",{className:"text-gray-600",children:"尝试调整搜索条件或筛选选项"})]})]})}},4653:(e,a,l)=>{"use strict";l.d(a,{A:()=>t});let t=(0,l(9946).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},5968:(e,a,l)=>{"use strict";l.d(a,{A:()=>t});let t=(0,l(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6932:(e,a,l)=>{"use strict";l.d(a,{A:()=>t});let t=(0,l(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7924:(e,a,l)=>{"use strict";l.d(a,{A:()=>t});let t=(0,l(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[6711,6874,2108,2313,3254,8441,1684,7358],()=>a(1987)),_N_E=e.O()}]);