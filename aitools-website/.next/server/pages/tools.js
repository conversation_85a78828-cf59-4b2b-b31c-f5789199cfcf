/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/tools";
exports.ids = ["pages/tools"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftools&preferredRegion=&absolutePagePath=.%2Fpages%2Ftools.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftools&preferredRegion=&absolutePagePath=.%2Fpages%2Ftools.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/tools.tsx */ \"(pages-dir-node)/./pages/tools.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/tools\",\n        pathname: \"/tools\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_tools_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftools&preferredRegion=&absolutePagePath=.%2Fpages%2Ftools.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/globals.css */ \"(pages-dir-node)/./src/app/globals.css\");\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_app_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_app.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_app.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ2tEO0FBQ3ZCO0FBRVosU0FBU0MsSUFBSSxFQUMxQkMsU0FBUyxFQUNUQyxXQUFXLEVBQUVDLE9BQU8sRUFBRSxHQUFHRCxXQUFXLEVBQzNCO0lBQ1QscUJBQ0UsOERBQUNILDREQUFlQTtRQUFDSSxTQUFTQTtrQkFDeEIsNEVBQUNGO1lBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9wYWdlcy9fYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCAnQC9hcHAvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoe1xuICBDb21wb25lbnQsXG4gIHBhZ2VQcm9wczogeyBzZXNzaW9uLCAuLi5wYWdlUHJvcHMgfSxcbn06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7a0NBQ0gsOERBQUNLO3dCQUFLQyxTQUFROzs7Ozs7a0NBQ2QsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRXhCLDhEQUFDQzs7a0NBQ0MsOERBQUNULCtDQUFJQTs7Ozs7a0NBQ0wsOERBQUNDLHFEQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJbkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9wYWdlcy9fZG9jdW1lbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJtZXRhIiwiY2hhclNldCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/tools.tsx":
/*!*************************!*\
  !*** ./pages/tools.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolsPage),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"(pages-dir-node)/./src/components/Layout.tsx\");\n/* harmony import */ var _components_ToolCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ToolCard */ \"(pages-dir-node)/./src/components/ToolCard.tsx\");\n/* harmony import */ var _constants_pricing__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/pricing */ \"(pages-dir-node)/./src/constants/pricing.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Filter,Grid,List,Search!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Filter,Grid,List,Search!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Filter,Grid,List,Search!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Filter,Grid,List,Search!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Filter,Grid,List,Search!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/mongodb */ \"(pages-dir-node)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Tool__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/models/Tool */ \"(pages-dir-node)/./src/models/Tool.ts\");\n\n\n\n\n\n\n\n\n// 工具目录页面组件\nconst categories = [\n    {\n        value: '',\n        label: '所有分类'\n    },\n    {\n        value: 'text-generation',\n        label: '文本生成'\n    },\n    {\n        value: 'image-generation',\n        label: '图像生成'\n    },\n    {\n        value: 'code-generation',\n        label: '代码生成'\n    },\n    {\n        value: 'data-analysis',\n        label: '数据分析'\n    },\n    {\n        value: 'audio-processing',\n        label: '音频处理'\n    },\n    {\n        value: 'video-editing',\n        label: '视频编辑'\n    }\n];\n// 使用统一的价格选项配置\nconst pricingOptions = _constants_pricing__WEBPACK_IMPORTED_MODULE_4__.TOOL_PRICING_OPTIONS;\nconst sortOptions = [\n    {\n        value: 'popular',\n        label: '最受欢迎'\n    },\n    {\n        value: 'newest',\n        label: '最新添加'\n    },\n    {\n        value: 'name',\n        label: '名称排序'\n    },\n    {\n        value: 'views',\n        label: '浏览量'\n    }\n];\nfunction ToolsPage({ tools, totalCount }) {\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedPricing, setSelectedPricing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('popular');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filter and sort tools\n    const filteredTools = tools.filter((tool)=>{\n        const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) || tool.description.toLowerCase().includes(searchTerm.toLowerCase()) || tool.tags.some((tag)=>tag.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesCategory = !selectedCategory || tool.category === selectedCategory;\n        const matchesPricing = !selectedPricing || tool.pricing === selectedPricing;\n        return matchesSearch && matchesCategory && matchesPricing;\n    });\n    const sortedTools = [\n        ...filteredTools\n    ].sort((a, b)=>{\n        switch(sortBy){\n            case 'popular':\n                return (b.likes || 0) - (a.likes || 0);\n            case 'views':\n                return (b.views || 0) - (a.views || 0);\n            case 'name':\n                return a.name.localeCompare(b.name);\n            case 'newest':\n                return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();\n            default:\n                return 0;\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: \"AI 工具目录\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600\",\n                            children: [\n                                \"发现 \",\n                                totalCount,\n                                \" 个精选的 AI 工具，提升您的工作效率\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索工具名称、描述或标签...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"absolute left-3 top-3 h-5 w-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowFilters(!showFilters),\n                                className: \"flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"筛选选项\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: `ml-2 h-4 w-4 transform ${showFilters ? 'rotate-180' : ''}`\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `grid grid-cols-1 md:grid-cols-4 gap-4 ${showFilters ? 'block' : 'hidden md:grid'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"分类\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>setSelectedCategory(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category.value,\n                                                    children: category.label\n                                                }, category.value, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"价格\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedPricing,\n                                            onChange: (e)=>setSelectedPricing(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: pricingOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"排序\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            children: sortOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option.value,\n                                                    children: option.label\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"视图\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex rounded-lg border border-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewMode('grid'),\n                                                    className: `flex-1 px-3 py-2 text-sm font-medium rounded-l-lg ${viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewMode('list'),\n                                                    className: `flex-1 px-3 py-2 text-sm font-medium rounded-r-lg ${viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"显示 \",\n                            sortedTools.length,\n                            \" 个结果\",\n                            searchTerm && ` 搜索 \"${searchTerm}\"`,\n                            selectedCategory && ` 在 \"${categories.find((c)=>c.value === selectedCategory)?.label}\"`\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this),\n                sortedTools.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4',\n                    children: sortedTools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            tool: tool\n                        }, tool._id, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Filter_Grid_List_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"未找到匹配的工具\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"尝试调整搜索条件或筛选选项\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/tools.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n// 服务端数据获取函数\nconst getStaticProps = async ()=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        // 获取所有已发布的工具\n        const tools = await _models_Tool__WEBPACK_IMPORTED_MODULE_6__[\"default\"].find({\n            status: 'published'\n        }).sort({\n            views: -1,\n            likes: -1,\n            publishedAt: -1\n        }).select('-submittedBy -reviewNotes -reviewedBy').lean();\n        // 获取工具总数\n        const totalCount = await _models_Tool__WEBPACK_IMPORTED_MODULE_6__[\"default\"].countDocuments({\n            status: 'published'\n        });\n        // 序列化数据以避免 Next.js 序列化问题\n        const serializedData = {\n            tools: JSON.parse(JSON.stringify(tools)),\n            totalCount\n        };\n        return {\n            props: serializedData,\n            // 重新验证时间：10分钟\n            revalidate: 600\n        };\n    } catch (error) {\n        console.error('Error fetching tools data:', error);\n        // 返回空数据而不是失败，确保页面能够渲染\n        return {\n            props: {\n                tools: [],\n                totalCount: 0\n            },\n            // 出错时更短的重新验证时间\n            revalidate: 60\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/tools.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_seo_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seo/PerformanceMonitor */ \"(pages-dir-node)/./src/components/seo/PerformanceMonitor.tsx\");\n\n\n\n\nconst Layout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-sm\",\n                                                        children: \"AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"发现最新最好的 AI 工具，提升您的工作效率和创造力。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\",\n                                            children: \"快速链接\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/tools\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"工具目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/categories\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"分类浏览\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/submit\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"提交工具\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\",\n                                            children: \"支持\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"帮助中心\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"联系我们\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"隐私政策\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 mt-8 pt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-gray-600\",\n                                children: \"\\xa9 2024 AI Tools Directory. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/Layout.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ToolCard.tsx":
/*!*************************************!*\
  !*** ./src/components/ToolCard.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _tools_LikeButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tools/LikeButton */ \"(pages-dir-node)/./src/components/tools/LikeButton.tsx\");\n/* harmony import */ var _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/OptimizedImage */ \"(pages-dir-node)/./src/components/ui/OptimizedImage.tsx\");\n/* harmony import */ var _constants_pricing__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/pricing */ \"(pages-dir-node)/./src/constants/pricing.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst ToolCard = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                tool.logo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: tool.logo,\n                                    alt: `${tool.name} logo`,\n                                    width: _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__.ImageSizes.toolLogo.width,\n                                    height: _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__.ImageSizes.toolLogo.height,\n                                    className: \"rounded-lg object-cover\",\n                                    sizes: _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__.ResponsiveSizes.toolLogo,\n                                    placeholder: \"blur\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-lg\",\n                                        children: tool.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: tool.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,_constants_pricing__WEBPACK_IMPORTED_MODULE_5__.getToolPricingColor)(tool.pricing)}`,\n                                            children: (0,_constants_pricing__WEBPACK_IMPORTED_MODULE_5__.getToolPricingText)(tool.pricing)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: tool.website,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-gray-400 hover:text-blue-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-sm mb-4 line-clamp-2\",\n                    children: tool.description\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-4\",\n                    children: [\n                        tool.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\",\n                                children: tag\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined)),\n                        tool.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\",\n                            children: [\n                                \"+\",\n                                tool.tags.length - 3\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tool.views\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tool.likes\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_LikeButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    toolId: tool._id,\n                                    initialLikes: tool.likes,\n                                    onLoginRequired: onLoginRequired,\n                                    onUnlike: onUnlike,\n                                    isInLikedPage: isInLikedPage\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/tools/${tool._id}`,\n                                    className: \"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\",\n                                    children: \"查看详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToolCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ToolCard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/seo/PerformanceMonitor.tsx":
/*!***************************************************!*\
  !*** ./src/components/seo/PerformanceMonitor.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceOptimizations: () => (/* binding */ PerformanceOptimizations),\n/* harmony export */   \"default\": () => (/* binding */ PerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default,PerformanceOptimizations auto */ \nfunction PerformanceMonitor() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PerformanceMonitor.useEffect\": ()=>{\n            // 只在生产环境中启用性能监控\n            if (true) {\n                return;\n            }\n            const metrics = {};\n            // 监控 First Contentful Paint (FCP)\n            const observeFCP = {\n                \"PerformanceMonitor.useEffect.observeFCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFCP\": (list)=>{\n                            const entries = list.getEntries();\n                            const fcpEntry = entries.find({\n                                \"PerformanceMonitor.useEffect.observeFCP.fcpEntry\": (entry)=>entry.name === 'first-contentful-paint'\n                            }[\"PerformanceMonitor.useEffect.observeFCP.fcpEntry\"]);\n                            if (fcpEntry) {\n                                metrics.fcp = fcpEntry.startTime;\n                                reportMetric('FCP', fcpEntry.startTime);\n                            }\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFCP\"];\n            // 监控 Largest Contentful Paint (LCP)\n            const observeLCP = {\n                \"PerformanceMonitor.useEffect.observeLCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeLCP\": (list)=>{\n                            const entries = list.getEntries();\n                            const lastEntry = entries[entries.length - 1];\n                            metrics.lcp = lastEntry.startTime;\n                            reportMetric('LCP', lastEntry.startTime);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeLCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'largest-contentful-paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeLCP\"];\n            // 监控 First Input Delay (FID)\n            const observeFID = {\n                \"PerformanceMonitor.useEffect.observeFID\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFID\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeFID\": (entry)=>{\n                                    metrics.fid = entry.processingStart - entry.startTime;\n                                    reportMetric('FID', entry.processingStart - entry.startTime);\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'first-input'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFID\"];\n            // 监控 Cumulative Layout Shift (CLS)\n            const observeCLS = {\n                \"PerformanceMonitor.useEffect.observeCLS\": ()=>{\n                    let clsValue = 0;\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeCLS\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeCLS\": (entry)=>{\n                                    if (!entry.hadRecentInput) {\n                                        clsValue += entry.value;\n                                    }\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                            metrics.cls = clsValue;\n                            reportMetric('CLS', clsValue);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'layout-shift'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeCLS\"];\n            // 监控 Time to First Byte (TTFB)\n            const observeTTFB = {\n                \"PerformanceMonitor.useEffect.observeTTFB\": ()=>{\n                    const navigationEntry = performance.getEntriesByType('navigation')[0];\n                    if (navigationEntry) {\n                        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n                        metrics.ttfb = ttfb;\n                        reportMetric('TTFB', ttfb);\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect.observeTTFB\"];\n            // 报告性能指标\n            const reportMetric = {\n                \"PerformanceMonitor.useEffect.reportMetric\": (name, value)=>{\n                    // 在开发环境中输出到控制台\n                    if (true) {\n                        console.log(`Performance Metric - ${name}:`, value);\n                    }\n                    // 在生产环境中可以发送到分析服务\n                    // 例如 Google Analytics, Vercel Analytics 等\n                    if (false) {}\n                }\n            }[\"PerformanceMonitor.useEffect.reportMetric\"];\n            // 检查浏览器支持\n            if (typeof PerformanceObserver !== 'undefined') {\n                observeFCP();\n                observeLCP();\n                observeFID();\n                observeCLS();\n            }\n            observeTTFB();\n            // 页面卸载时报告最终指标\n            const reportFinalMetrics = {\n                \"PerformanceMonitor.useEffect.reportFinalMetrics\": ()=>{\n                    if (Object.keys(metrics).length > 0) {\n                        // 可以发送到分析服务\n                        console.log('Final Performance Metrics:', metrics);\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect.reportFinalMetrics\"];\n            window.addEventListener('beforeunload', reportFinalMetrics);\n            return ({\n                \"PerformanceMonitor.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', reportFinalMetrics);\n                }\n            })[\"PerformanceMonitor.useEffect\"];\n        }\n    }[\"PerformanceMonitor.useEffect\"], []);\n    return null; // 这是一个无UI的监控组件\n}\n// 性能优化建议\nconst PerformanceOptimizations = {\n    // FCP 优化建议\n    fcp: {\n        good: 1800,\n        needsImprovement: 3000,\n        suggestions: [\n            '减少服务器响应时间',\n            '消除阻塞渲染的资源',\n            '压缩CSS和JavaScript',\n            '使用CDN加速资源加载'\n        ]\n    },\n    // LCP 优化建议\n    lcp: {\n        good: 2500,\n        needsImprovement: 4000,\n        suggestions: [\n            '优化图片加载',\n            '预加载关键资源',\n            '减少JavaScript执行时间',\n            '使用服务端渲染'\n        ]\n    },\n    // FID 优化建议\n    fid: {\n        good: 100,\n        needsImprovement: 300,\n        suggestions: [\n            '减少JavaScript执行时间',\n            '分割长任务',\n            '使用Web Workers',\n            '延迟加载非关键JavaScript'\n        ]\n    },\n    // CLS 优化建议\n    cls: {\n        good: 0.1,\n        needsImprovement: 0.25,\n        suggestions: [\n            '为图片和视频设置尺寸属性',\n            '避免在现有内容上方插入内容',\n            '使用transform动画而非改变布局的动画',\n            '预留广告位空间'\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/seo/PerformanceMonitor.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/tools/LikeButton.tsx":
/*!*********************************************!*\
  !*** ./src/components/tools/LikeButton.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LikeButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaHeart_FaRegHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaHeart,FaRegHeart!=!react-icons/fa */ \"(pages-dir-node)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LikeButton({ toolId, initialLikes = 0, initialLiked = false, onLoginRequired, onUnlike, isInLikedPage = false }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [liked, setLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLiked);\n    const [likes, setLikes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLikes);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 获取点赞状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LikeButton.useEffect\": ()=>{\n            const fetchLikeStatus = {\n                \"LikeButton.useEffect.fetchLikeStatus\": async ()=>{\n                    try {\n                        const response = await fetch(`/api/tools/${toolId}/like`);\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success) {\n                                setLiked(data.data.liked);\n                                setLikes(data.data.likes);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch like status:', error);\n                    }\n                }\n            }[\"LikeButton.useEffect.fetchLikeStatus\"];\n            if (session) {\n                fetchLikeStatus();\n            }\n        }\n    }[\"LikeButton.useEffect\"], [\n        toolId,\n        session\n    ]);\n    const handleLike = async ()=>{\n        if (!session) {\n            onLoginRequired?.();\n            return;\n        }\n        if (isLoading) return;\n        setIsLoading(true);\n        try {\n            // 如果在liked页面，发送强制unlike请求\n            const requestBody = isInLikedPage ? {\n                forceUnlike: true\n            } : {};\n            const response = await fetch(`/api/tools/${toolId}/like`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    const newLikedState = data.data.liked;\n                    setLiked(newLikedState);\n                    setLikes(data.data.likes);\n                    // 如果用户取消了点赞，并且提供了onUnlike回调，则调用它\n                    if (!newLikedState && onUnlike) {\n                        onUnlike(toolId);\n                    }\n                }\n            } else {\n                const errorData = await response.json();\n                console.error('Like failed:', errorData.message);\n            }\n        } catch (error) {\n            console.error('Like request failed:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleLike,\n        disabled: isLoading,\n        className: `\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ${liked ? 'bg-red-50 text-red-600 hover:bg-red-100' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}\n        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}\n        border border-gray-200 hover:border-gray-300\n      `,\n        children: [\n            liked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_FaRegHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHeart, {\n                className: \"w-4 h-4 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_FaRegHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRegHeart, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium\",\n                children: likes > 0 ? likes : ''\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/tools/LikeButton.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ui/OptimizedImage.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/OptimizedImage.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSizes: () => (/* binding */ ImageSizes),\n/* harmony export */   ResponsiveSizes: () => (/* binding */ ResponsiveSizes),\n/* harmony export */   \"default\": () => (/* binding */ OptimizedImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default,ImageSizes,ResponsiveSizes auto */ \n\n\nfunction OptimizedImage({ src, alt, width, height, className = '', priority = false, fill = false, sizes, placeholder = 'empty', blurDataURL, fallbackSrc = '/images/placeholder.svg', onError }) {\n    const [imgSrc, setImgSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(src);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleError = ()=>{\n        setHasError(true);\n        setIsLoading(false);\n        setImgSrc(fallbackSrc);\n        onError?.();\n    };\n    const handleLoad = ()=>{\n        setIsLoading(false);\n    };\n    // 生成模糊占位符\n    const generateBlurDataURL = (w = 10, h = 10)=>{\n        const canvas = document.createElement('canvas');\n        canvas.width = w;\n        canvas.height = h;\n        const ctx = canvas.getContext('2d');\n        if (ctx) {\n            ctx.fillStyle = '#f3f4f6';\n            ctx.fillRect(0, 0, w, h);\n        }\n        return canvas.toDataURL();\n    };\n    const imageProps = {\n        src: imgSrc,\n        alt,\n        className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,\n        onError: handleError,\n        onLoad: handleLoad,\n        priority,\n        placeholder: placeholder === 'blur' ? 'blur' : 'empty',\n        blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n        sizes: sizes || (fill ? '100vw' : undefined)\n    };\n    if (fill) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    ...imageProps,\n                    fill: true,\n                    style: {\n                        objectFit: 'cover'\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gray-200 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                ...imageProps,\n                width: width,\n                height: height\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-200 animate-pulse\",\n                style: {\n                    width,\n                    height\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n// 预设的图片尺寸配置\nconst ImageSizes = {\n    avatar: {\n        width: 40,\n        height: 40\n    },\n    avatarLarge: {\n        width: 80,\n        height: 80\n    },\n    toolLogo: {\n        width: 64,\n        height: 64\n    },\n    toolLogoLarge: {\n        width: 128,\n        height: 128\n    },\n    thumbnail: {\n        width: 200,\n        height: 150\n    },\n    card: {\n        width: 300,\n        height: 200\n    },\n    hero: {\n        width: 1200,\n        height: 600\n    }\n};\n// 响应式图片尺寸字符串\nconst ResponsiveSizes = {\n    avatar: '40px',\n    toolLogo: '64px',\n    thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n    card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n    hero: '100vw',\n    full: '100vw'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ui/OptimizedImage.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/constants/pricing.ts":
/*!**********************************!*\
  !*** ./src/constants/pricing.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LAUNCH_OPTIONS: () => (/* binding */ LAUNCH_OPTIONS),\n/* harmony export */   PRICING_CONFIG: () => (/* binding */ PRICING_CONFIG),\n/* harmony export */   TOOL_PRICING_FORM_OPTIONS: () => (/* binding */ TOOL_PRICING_FORM_OPTIONS),\n/* harmony export */   TOOL_PRICING_OPTIONS: () => (/* binding */ TOOL_PRICING_OPTIONS),\n/* harmony export */   TOOL_PRICING_TYPES: () => (/* binding */ TOOL_PRICING_TYPES),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatStripeAmount: () => (/* binding */ formatStripeAmount),\n/* harmony export */   getPricingConfig: () => (/* binding */ getPricingConfig),\n/* harmony export */   getToolPricingColor: () => (/* binding */ getToolPricingColor),\n/* harmony export */   getToolPricingText: () => (/* binding */ getToolPricingText)\n/* harmony export */ });\n/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */ // 基础价格配置\nconst PRICING_CONFIG = {\n    // 优先发布服务价格\n    PRIORITY_LAUNCH: {\n        // 显示价格（元）\n        displayPrice: 19.9,\n        // Stripe价格（分为单位）\n        stripeAmount: 1990,\n        // 货币\n        currency: 'USD',\n        // Stripe货币代码（小写）\n        stripeCurrency: 'usd',\n        // 产品名称\n        productName: 'AI工具优先发布服务',\n        // 产品描述\n        description: '让您的AI工具获得优先审核和推荐位置',\n        // 功能特性\n        features: [\n            '可选择任意发布日期',\n            '优先审核处理',\n            '首页推荐位置',\n            '专属客服支持'\n        ]\n    },\n    // 免费发布配置\n    FREE_LAUNCH: {\n        displayPrice: 0,\n        stripeAmount: 0,\n        currency: 'USD',\n        stripeCurrency: 'usd',\n        productName: '免费发布服务',\n        description: '选择一个月后的任意发布日期',\n        features: [\n            '免费提交审核',\n            '发布日期：一个月后起',\n            '正常审核流程',\n            '标准展示位置'\n        ]\n    }\n};\n// 发布选项配置\nconst LAUNCH_OPTIONS = [\n    {\n        id: 'free',\n        title: '免费发布',\n        description: PRICING_CONFIG.FREE_LAUNCH.description,\n        price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n        features: PRICING_CONFIG.FREE_LAUNCH.features\n    },\n    {\n        id: 'paid',\n        title: '优先发布',\n        description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n        price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n        features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n        recommended: true\n    }\n];\n// 工具定价类型配置\nconst TOOL_PRICING_TYPES = {\n    FREE: {\n        value: 'free',\n        label: '免费',\n        color: 'bg-green-100 text-green-800'\n    },\n    FREEMIUM: {\n        value: 'freemium',\n        label: '免费增值',\n        color: 'bg-blue-100 text-blue-800'\n    },\n    PAID: {\n        value: 'paid',\n        label: '付费',\n        color: 'bg-orange-100 text-orange-800'\n    }\n};\n// 工具定价选项（用于筛选）\nconst TOOL_PRICING_OPTIONS = [\n    {\n        value: '',\n        label: '所有价格'\n    },\n    {\n        value: TOOL_PRICING_TYPES.FREE.value,\n        label: TOOL_PRICING_TYPES.FREE.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.FREEMIUM.value,\n        label: TOOL_PRICING_TYPES.FREEMIUM.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.PAID.value,\n        label: TOOL_PRICING_TYPES.PAID.label\n    }\n];\n// 工具定价选项（用于表单）\nconst TOOL_PRICING_FORM_OPTIONS = [\n    {\n        value: TOOL_PRICING_TYPES.FREE.value,\n        label: TOOL_PRICING_TYPES.FREE.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.FREEMIUM.value,\n        label: TOOL_PRICING_TYPES.FREEMIUM.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.PAID.value,\n        label: TOOL_PRICING_TYPES.PAID.label\n    }\n];\n// 辅助函数\nconst getPricingConfig = (optionId)=>{\n    return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\nconst getToolPricingColor = (pricing)=>{\n    switch(pricing){\n        case TOOL_PRICING_TYPES.FREE.value:\n            return TOOL_PRICING_TYPES.FREE.color;\n        case TOOL_PRICING_TYPES.FREEMIUM.value:\n            return TOOL_PRICING_TYPES.FREEMIUM.color;\n        case TOOL_PRICING_TYPES.PAID.value:\n            return TOOL_PRICING_TYPES.PAID.color;\n        default:\n            return 'bg-gray-100 text-gray-800';\n    }\n};\nconst getToolPricingText = (pricing)=>{\n    switch(pricing){\n        case TOOL_PRICING_TYPES.FREE.value:\n            return TOOL_PRICING_TYPES.FREE.label;\n        case TOOL_PRICING_TYPES.FREEMIUM.value:\n            return TOOL_PRICING_TYPES.FREEMIUM.label;\n        case TOOL_PRICING_TYPES.PAID.value:\n            return TOOL_PRICING_TYPES.PAID.label;\n        default:\n            return pricing;\n    }\n};\n// 格式化价格显示\nconst formatPrice = (price)=>{\n    return price === 0 ? '免费' : `¥${price}`;\n};\n// 格式化Stripe金额显示\nconst formatStripeAmount = (amount, currency = 'cny')=>{\n    return new Intl.NumberFormat('zh-CN', {\n        style: 'currency',\n        currency: currency.toUpperCase(),\n        minimumFractionDigits: 2\n    }).format(amount / 100);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/constants/pricing.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function dbConnect() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/models/Tool.ts":
/*!****************************!*\
  !*** ./src/models/Tool.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ToolSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Tool name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Tool name cannot exceed 100 characters'\n        ]\n    },\n    tagline: {\n        type: String,\n        trim: true,\n        maxlength: [\n            200,\n            'Tagline cannot exceed 200 characters'\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            'Tool description is required'\n        ],\n        trim: true,\n        maxlength: [\n            500,\n            'Description cannot exceed 500 characters'\n        ]\n    },\n    longDescription: {\n        type: String,\n        trim: true,\n        maxlength: [\n            2000,\n            'Long description cannot exceed 2000 characters'\n        ]\n    },\n    website: {\n        type: String,\n        required: [\n            true,\n            'Website URL is required'\n        ],\n        trim: true,\n        validate: {\n            validator: function(v) {\n                return /^https?:\\/\\/.+/.test(v);\n            },\n            message: 'Please enter a valid URL'\n        }\n    },\n    logo: {\n        type: String,\n        trim: true\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: [\n            'text-generation',\n            'image-generation',\n            'video-generation',\n            'audio-generation',\n            'code-generation',\n            'data-analysis',\n            'productivity',\n            'design',\n            'marketing',\n            'education',\n            'research',\n            'other'\n        ]\n    },\n    tags: [\n        {\n            type: String,\n            trim: true,\n            lowercase: true\n        }\n    ],\n    pricing: {\n        type: String,\n        required: [\n            true,\n            'Pricing model is required'\n        ],\n        enum: [\n            'free',\n            'freemium',\n            'paid'\n        ]\n    },\n    pricingDetails: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Pricing details cannot exceed 500 characters'\n        ]\n    },\n    screenshots: [\n        {\n            type: String,\n            trim: true\n        }\n    ],\n    submittedBy: {\n        type: String,\n        required: [\n            true,\n            'Submitter ID is required'\n        ],\n        trim: true\n    },\n    submittedAt: {\n        type: Date,\n        default: Date.now\n    },\n    publishedAt: {\n        type: Date\n    },\n    status: {\n        type: String,\n        required: true,\n        enum: [\n            'draft',\n            'pending',\n            'approved',\n            'published',\n            'rejected'\n        ],\n        default: 'draft'\n    },\n    reviewNotes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            1000,\n            'Review notes cannot exceed 1000 characters'\n        ]\n    },\n    reviewedBy: {\n        type: String,\n        trim: true\n    },\n    reviewedAt: {\n        type: Date\n    },\n    // 发布日期选择相关\n    launchDateSelected: {\n        type: Boolean,\n        default: false\n    },\n    selectedLaunchDate: {\n        type: Date\n    },\n    launchOption: {\n        type: String,\n        enum: [\n            'free',\n            'paid'\n        ]\n    },\n    // 付费相关\n    paymentRequired: {\n        type: Boolean,\n        default: false\n    },\n    paymentAmount: {\n        type: Number,\n        min: 0\n    },\n    paymentStatus: {\n        type: String,\n        enum: [\n            'pending',\n            'completed',\n            'failed',\n            'refunded'\n        ]\n    },\n    orderId: {\n        type: String,\n        trim: true\n    },\n    paymentMethod: {\n        type: String,\n        trim: true\n    },\n    paidAt: {\n        type: Date\n    },\n    views: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    likes: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    likedBy: [\n        {\n            type: String,\n            trim: true\n        }\n    ],\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nToolSchema.index({\n    status: 1,\n    isActive: 1\n});\nToolSchema.index({\n    category: 1,\n    status: 1\n});\nToolSchema.index({\n    tags: 1,\n    status: 1\n});\nToolSchema.index({\n    submittedBy: 1\n});\nToolSchema.index({\n    publishedAt: -1\n});\nToolSchema.index({\n    views: -1\n});\nToolSchema.index({\n    likes: -1\n});\n// Text search index\nToolSchema.index({\n    name: 'text',\n    tagline: 'text',\n    description: 'text',\n    longDescription: 'text',\n    tags: 'text'\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Tool || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Tool', ToolSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/models/Tool.ts\n");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftools&preferredRegion=&absolutePagePath=.%2Fpages%2Ftools.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();