/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"(pages-dir-node)/./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/globals.css */ \"(pages-dir-node)/./src/app/globals.css\");\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_app_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_app.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_app.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQ2tEO0FBQ3ZCO0FBRVosU0FBU0MsSUFBSSxFQUMxQkMsU0FBUyxFQUNUQyxXQUFXLEVBQUVDLE9BQU8sRUFBRSxHQUFHRCxXQUFXLEVBQzNCO0lBQ1QscUJBQ0UsOERBQUNILDREQUFlQTtRQUFDSSxTQUFTQTtrQkFDeEIsNEVBQUNGO1lBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9wYWdlcy9fYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCAnQC9hcHAvZ2xvYmFscy5jc3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoe1xuICBDb21wb25lbnQsXG4gIHBhZ2VQcm9wczogeyBzZXNzaW9uLCAuLi5wYWdlUHJvcHMgfSxcbn06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"zh-CN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7a0NBQ0gsOERBQUNLO3dCQUFLQyxTQUFROzs7Ozs7a0NBQ2QsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRXhCLDhEQUFDQzs7a0NBQ0MsOERBQUNULCtDQUFJQTs7Ozs7a0NBQ0wsOERBQUNDLHFEQUFVQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJbkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9wYWdlcy9fZG9jdW1lbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJtZXRhIiwiY2hhclNldCIsImxpbmsiLCJyZWwiLCJocmVmIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Layout */ \"(pages-dir-node)/./src/components/Layout.tsx\");\n/* harmony import */ var _components_ToolCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ToolCard */ \"(pages-dir-node)/./src/components/ToolCard.tsx\");\n/* harmony import */ var _components_CategoryCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CategoryCard */ \"(pages-dir-node)/./src/components/CategoryCard.tsx\");\n/* harmony import */ var _components_ErrorMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ErrorMessage */ \"(pages-dir-node)/./src/components/ErrorMessage.tsx\");\n/* harmony import */ var _components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/LoginModal */ \"(pages-dir-node)/./src/components/auth/LoginModal.tsx\");\n/* harmony import */ var _lib_seo_structuredData__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/seo/structuredData */ \"(pages-dir-node)/./src/lib/seo/structuredData.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Search,Star,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Search,Star,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Search,Star,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Search,Star,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Search,Star,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Search,Star,TrendingUp,Zap!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/mongodb */ \"(pages-dir-node)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Tool__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/models/Tool */ \"(pages-dir-node)/./src/models/Tool.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n// 分类数据\nconst categories = [\n    {\n        _id: '1',\n        name: '文本生成',\n        slug: 'text-generation',\n        description: 'AI tools for generating and editing text content',\n        icon: '📝',\n        color: '#3B82F6',\n        toolCount: 25\n    },\n    {\n        _id: '2',\n        name: '图像生成',\n        slug: 'image-generation',\n        description: 'AI tools for creating and editing images',\n        icon: '🎨',\n        color: '#8B5CF6',\n        toolCount: 18\n    },\n    {\n        _id: '3',\n        name: '代码生成',\n        slug: 'code-generation',\n        description: 'AI tools for writing and debugging code',\n        icon: '💻',\n        color: '#F59E0B',\n        toolCount: 12\n    },\n    {\n        _id: '4',\n        name: '数据分析',\n        slug: 'data-analysis',\n        description: 'AI tools for analyzing and visualizing data',\n        icon: '📊',\n        color: '#06B6D4',\n        toolCount: 15\n    }\n];\nfunction Home({ featuredTools, todayTools, recentTools }) {\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoginModalOpen, setIsLoginModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 生成结构化数据\n    const websiteStructuredData = (0,_lib_seo_structuredData__WEBPACK_IMPORTED_MODULE_8__.getWebsiteStructuredData)();\n    const organizationStructuredData = (0,_lib_seo_structuredData__WEBPACK_IMPORTED_MODULE_8__.getOrganizationStructuredData)();\n    const allTools = [\n        ...featuredTools,\n        ...todayTools,\n        ...recentTools\n    ];\n    const toolListStructuredData = allTools.length > 0 ? (0,_lib_seo_structuredData__WEBPACK_IMPORTED_MODULE_8__.getToolListStructuredData)(allTools) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(websiteStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(organizationStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            toolListStructuredData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(toolListStructuredData)\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorMessage__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    message: error,\n                    onClose: ()=>setError('')\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\",\n                role: \"banner\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                                children: [\n                                    \"发现最好的\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-blue-600\",\n                                        children: \" AI 工具\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美工具。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-2xl mx-auto mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"搜索 AI 工具、分类或功能...\",\n                                            className: \"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"absolute left-4 top-4 h-6 w-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/tools\",\n                                        className: \"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"浏览所有工具\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/submit\",\n                                        className: \"inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors\",\n                                        children: \"提交您的工具\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            todayTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gradient-to-r from-green-50 to-blue-50\",\n                \"aria-labelledby\": \"today-tools-heading\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    id: \"today-tools-heading\",\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"inline-block mr-2 h-8 w-8 text-green-600\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"今日发布\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"今天刚刚发布的最新 AI 工具\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            role: \"list\",\n                            \"aria-label\": \"今日发布的AI工具\",\n                            children: todayTools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    role: \"listitem\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        tool: tool,\n                                        onLoginRequired: ()=>setIsLoginModalOpen(true)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 19\n                                    }, this)\n                                }, tool._id, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 151,\n                columnNumber: 9\n            }, this),\n            recentTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                \"aria-labelledby\": \"recent-tools-heading\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    id: \"recent-tools-heading\",\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"inline-block mr-2 h-8 w-8 text-blue-600\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"最近发布\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"过去一周内发布的新 AI 工具\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: recentTools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    tool: tool,\n                                    onLoginRequired: ()=>setIsLoginModalOpen(true)\n                                }, tool._id, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/tools?sort=publishedAt&order=desc\",\n                                className: \"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\",\n                                children: \"查看更多最新工具\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"inline-block mr-2 h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"热门工具\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"最受欢迎和评价最高的 AI 工具\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: featuredTools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    tool: tool,\n                                    onLoginRequired: ()=>setIsLoginModalOpen(true)\n                                }, tool._id, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/tools\",\n                                className: \"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\",\n                                children: \"查看更多工具\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Search_Star_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"inline-block mr-2 h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"热门分类\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600\",\n                                    children: \"按功能分类浏览 AI 工具\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CategoryCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    category: category\n                                }, category._id, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/categories\",\n                                className: \"inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-lg text-blue-600 hover:bg-blue-50 transition-colors\",\n                                children: \"查看所有分类\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-blue-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-white mb-2\",\n                                        children: \"500+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"AI 工具\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-white mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"工具分类\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold text-white mb-2\",\n                                        children: \"10K+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-100\",\n                                        children: \"用户访问\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isLoginModalOpen,\n                onClose: ()=>setIsLoginModalOpen(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/pages/index.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n// 服务端数据获取函数\nconst getStaticProps = async ()=>{\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n        // 准备日期参数\n        const today = new Date();\n        const todayStart = new Date(today);\n        todayStart.setHours(0, 0, 0, 0);\n        const todayEnd = new Date(today);\n        todayEnd.setHours(23, 59, 59, 999);\n        const sevenDaysAgo = new Date();\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n        sevenDaysAgo.setHours(0, 0, 0, 0);\n        // 并行获取所有数据\n        const [featuredTools, todayTools, recentTools] = await Promise.all([\n            // 热门工具 - 按浏览量排序\n            _models_Tool__WEBPACK_IMPORTED_MODULE_10__[\"default\"].find({\n                status: 'published'\n            }).sort({\n                views: -1,\n                likes: -1\n            }).limit(6).select('-submittedBy -reviewNotes -reviewedBy').lean(),\n            // 当日发布的工具\n            _models_Tool__WEBPACK_IMPORTED_MODULE_10__[\"default\"].find({\n                status: 'published',\n                publishedAt: {\n                    $gte: todayStart,\n                    $lte: todayEnd\n                }\n            }).sort({\n                publishedAt: -1\n            }).limit(6).select('-submittedBy -reviewNotes -reviewedBy').lean(),\n            // 最近发布的工具 - 过去一周\n            _models_Tool__WEBPACK_IMPORTED_MODULE_10__[\"default\"].find({\n                status: 'published',\n                publishedAt: {\n                    $gte: sevenDaysAgo\n                }\n            }).sort({\n                publishedAt: -1\n            }).limit(6).select('-submittedBy -reviewNotes -reviewedBy').lean()\n        ]);\n        // 序列化数据以避免 Next.js 序列化问题\n        const serializedData = {\n            featuredTools: JSON.parse(JSON.stringify(featuredTools)),\n            todayTools: JSON.parse(JSON.stringify(todayTools)),\n            recentTools: JSON.parse(JSON.stringify(recentTools))\n        };\n        return {\n            props: serializedData,\n            // 重新验证时间：5分钟\n            revalidate: 300\n        };\n    } catch (error) {\n        console.error('Error fetching homepage data:', error);\n        // 返回空数据而不是失败，确保页面能够渲染\n        return {\n            props: {\n                featuredTools: [],\n                todayTools: [],\n                recentTools: []\n            },\n            // 出错时更短的重新验证时间\n            revalidate: 60\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNYO0FBRVk7QUFDSTtBQUNRO0FBQ0E7QUFDQztBQUV3RTtBQUNoRDtBQUN4QztBQUNBO0FBRXRDLE9BQU87QUFDUCxNQUFNbUIsYUFBYTtJQUNqQjtRQUNFQyxLQUFLO1FBQ0xDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxXQUFXO0lBQ2I7SUFDQTtRQUNFTixLQUFLO1FBQ0xDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxXQUFXO0lBQ2I7SUFDQTtRQUNFTixLQUFLO1FBQ0xDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxXQUFXO0lBQ2I7SUFDQTtRQUNFTixLQUFLO1FBQ0xDLE1BQU07UUFDTkMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxXQUFXO0lBQ2I7Q0FDRDtBQVFjLFNBQVNDLEtBQUssRUFBRUMsYUFBYSxFQUFFQyxVQUFVLEVBQUVDLFdBQVcsRUFBYTtJQUNoRixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2dDLGtCQUFrQkMsb0JBQW9CLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUV6RCxVQUFVO0lBQ1YsTUFBTWtDLHdCQUF3QjNCLGlGQUF3QkE7SUFDdEQsTUFBTTRCLDZCQUE2QjNCLHNGQUE2QkE7SUFDaEUsTUFBTTRCLFdBQVc7V0FBSVQ7V0FBa0JDO1dBQWVDO0tBQVk7SUFDbEUsTUFBTVEseUJBQXlCRCxTQUFTRSxNQUFNLEdBQUcsSUFBSTdCLGtGQUF5QkEsQ0FBQzJCLFlBQVk7SUFFM0YscUJBQ0UsOERBQUNsQywwREFBTUE7OzBCQUVMLDhEQUFDcUM7Z0JBQ0NDLE1BQUs7Z0JBQ0xDLHlCQUF5QjtvQkFDdkJDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQ1Y7Z0JBQ3pCOzs7Ozs7MEJBRUYsOERBQUNLO2dCQUNDQyxNQUFLO2dCQUNMQyx5QkFBeUI7b0JBQ3ZCQyxRQUFRQyxLQUFLQyxTQUFTLENBQUNUO2dCQUN6Qjs7Ozs7O1lBRURFLHdDQUNDLDhEQUFDRTtnQkFDQ0MsTUFBSztnQkFDTEMseUJBQXlCO29CQUN2QkMsUUFBUUMsS0FBS0MsU0FBUyxDQUFDUDtnQkFDekI7Ozs7OztZQUtIUCx1QkFDQyw4REFBQ2U7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUN6QyxnRUFBWUE7b0JBQ1gwQyxTQUFTakI7b0JBQ1RrQixTQUFTLElBQU1qQixTQUFTOzs7Ozs7Ozs7OzswQkFNOUIsOERBQUNrQjtnQkFBUUgsV0FBVTtnQkFBcURJLE1BQUs7MEJBQzNFLDRFQUFDTDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0s7d0JBQU9MLFdBQVU7OzBDQUNoQiw4REFBQ007Z0NBQUdOLFdBQVU7O29DQUFvRDtrREFFaEUsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUVsQyw4REFBQ1E7Z0NBQUVSLFdBQVU7MENBQStDOzs7Ozs7MENBSzVELDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDUzs0Q0FDQ2YsTUFBSzs0Q0FDTGdCLGFBQVk7NENBQ1pWLFdBQVU7Ozs7OztzREFFWiw4REFBQ3BDLHNIQUFNQTs0Q0FBQ29DLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUt0Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDN0Msa0RBQUlBO3dDQUNId0QsTUFBSzt3Q0FDTFgsV0FBVTs7MERBRVYsOERBQUNqQyxzSEFBR0E7Z0RBQUNpQyxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7O2tEQUdsQyw4REFBQzdDLGtEQUFJQTt3Q0FDSHdELE1BQUs7d0NBQ0xYLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFTUmxCLFdBQVdVLE1BQU0sR0FBRyxtQkFDbkIsOERBQUNXO2dCQUFRSCxXQUFVO2dCQUFrRFksbUJBQWdCOzBCQUNuRiw0RUFBQ2I7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSzs0QkFBT0wsV0FBVTs7OENBQ2hCLDhEQUFDYTtvQ0FBR0MsSUFBRztvQ0FBc0JkLFdBQVU7O3NEQUNyQyw4REFBQ2hDLHNIQUFRQTs0Q0FBQ2dDLFdBQVU7NENBQTJDZSxlQUFZOzs7Ozs7d0NBQVM7Ozs7Ozs7OENBR3RGLDhEQUFDUDtvQ0FBRVIsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FLdkMsOERBQUNEOzRCQUFJQyxXQUFVOzRCQUF1REksTUFBSzs0QkFBT1ksY0FBVztzQ0FDMUZsQyxXQUFXbUMsR0FBRyxDQUFDLENBQUNDLHFCQUNmLDhEQUFDQztvQ0FBdUJmLE1BQUs7OENBQzNCLDRFQUFDL0MsNERBQVFBO3dDQUNQNkQsTUFBTUE7d0NBQ05FLGlCQUFpQixJQUFNakMsb0JBQW9COzs7Ozs7bUNBSGpDK0IsS0FBSzdDLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQWEvQlUsWUFBWVMsTUFBTSxHQUFHLG1CQUNwQiw4REFBQ1c7Z0JBQVFILFdBQVU7Z0JBQWlCWSxtQkFBZ0I7MEJBQ2xELDRFQUFDYjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNLOzRCQUFPTCxXQUFVOzs4Q0FDaEIsOERBQUNhO29DQUFHQyxJQUFHO29DQUF1QmQsV0FBVTs7c0RBQ3RDLDhEQUFDL0Isc0hBQUtBOzRDQUFDK0IsV0FBVTs0Q0FBMENlLGVBQVk7Ozs7Ozt3Q0FBUzs7Ozs7Ozs4Q0FHbEYsOERBQUNQO29DQUFFUixXQUFVOzhDQUF3Qjs7Ozs7Ozs7Ozs7O3NDQUt2Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pqQixZQUFZa0MsR0FBRyxDQUFDLENBQUNDLHFCQUNoQiw4REFBQzdELDREQUFRQTtvQ0FFUDZELE1BQU1BO29DQUNORSxpQkFBaUIsSUFBTWpDLG9CQUFvQjttQ0FGdEMrQixLQUFLN0MsR0FBRzs7Ozs7Ozs7OztzQ0FPbkIsOERBQUMwQjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzdDLGtEQUFJQTtnQ0FDSHdELE1BQUs7Z0NBQ0xYLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1QsOERBQUNHO2dCQUFRSCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNhO29DQUFHYixXQUFVOztzREFDWiw4REFBQ25DLHNIQUFVQTs0Q0FBQ21DLFdBQVU7Ozs7Ozt3Q0FBNEM7Ozs7Ozs7OENBR3BFLDhEQUFDUTtvQ0FBRVIsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OztzQ0FLdkMsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNabkIsY0FBY29DLEdBQUcsQ0FBQyxDQUFDQyxxQkFDbEIsOERBQUM3RCw0REFBUUE7b0NBRVA2RCxNQUFNQTtvQ0FDTkUsaUJBQWlCLElBQU1qQyxvQkFBb0I7bUNBRnRDK0IsS0FBSzdDLEdBQUc7Ozs7Ozs7Ozs7c0NBT25CLDhEQUFDMEI7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUM3QyxrREFBSUE7Z0NBQ0h3RCxNQUFLO2dDQUNMWCxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFQLDhEQUFDRztnQkFBUUgsV0FBVTswQkFDakIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYTtvQ0FBR2IsV0FBVTs7c0RBQ1osOERBQUNsQyxzSEFBSUE7NENBQUNrQyxXQUFVOzs7Ozs7d0NBQTRDOzs7Ozs7OzhDQUc5RCw4REFBQ1E7b0NBQUVSLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7c0NBS3ZDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjVCLFdBQVc2QyxHQUFHLENBQUMsQ0FBQ0kseUJBQ2YsOERBQUMvRCxnRUFBWUE7b0NBQW9CK0QsVUFBVUE7bUNBQXhCQSxTQUFTaEQsR0FBRzs7Ozs7Ozs7OztzQ0FJbkMsOERBQUMwQjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzdDLGtEQUFJQTtnQ0FDSHdELE1BQUs7Z0NBQ0xYLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVAsOERBQUNHO2dCQUFRSCxXQUFVOzBCQUNqQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBcUM7Ozs7OztrREFDcEQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUVqQyw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBcUM7Ozs7OztrREFDcEQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUVqQyw4REFBQ0Q7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBcUM7Ozs7OztrREFDcEQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdkMsOERBQUN4QyxtRUFBVUE7Z0JBQ1Q4RCxRQUFRcEM7Z0JBQ1JnQixTQUFTLElBQU1mLG9CQUFvQjs7Ozs7Ozs7Ozs7O0FBSTNDO0FBRUEsWUFBWTtBQUNMLE1BQU1vQyxpQkFBNEM7SUFDdkQsSUFBSTtRQUNGLE1BQU1yRCx3REFBU0E7UUFFZixTQUFTO1FBQ1QsTUFBTXNELFFBQVEsSUFBSUM7UUFDbEIsTUFBTUMsYUFBYSxJQUFJRCxLQUFLRDtRQUM1QkUsV0FBV0MsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1FBQzdCLE1BQU1DLFdBQVcsSUFBSUgsS0FBS0Q7UUFDMUJJLFNBQVNELFFBQVEsQ0FBQyxJQUFJLElBQUksSUFBSTtRQUU5QixNQUFNRSxlQUFlLElBQUlKO1FBQ3pCSSxhQUFhQyxPQUFPLENBQUNELGFBQWFFLE9BQU8sS0FBSztRQUM5Q0YsYUFBYUYsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1FBRS9CLFdBQVc7UUFDWCxNQUFNLENBQUM5QyxlQUFlQyxZQUFZQyxZQUFZLEdBQUcsTUFBTWlELFFBQVFDLEdBQUcsQ0FBQztZQUNqRSxnQkFBZ0I7WUFDaEI5RCxxREFBU0EsQ0FBQytELElBQUksQ0FBQztnQkFBRUMsUUFBUTtZQUFZLEdBQ2xDQyxJQUFJLENBQUM7Z0JBQUVDLE9BQU8sQ0FBQztnQkFBR0MsT0FBTyxDQUFDO1lBQUUsR0FDNUJDLEtBQUssQ0FBQyxHQUNOQyxNQUFNLENBQUMseUNBQ1BDLElBQUk7WUFFUCxVQUFVO1lBQ1Z0RSxxREFBU0EsQ0FBQytELElBQUksQ0FBQztnQkFDYkMsUUFBUTtnQkFDUk8sYUFBYTtvQkFDWEMsTUFBTWpCO29CQUNOa0IsTUFBTWhCO2dCQUNSO1lBQ0YsR0FDR1EsSUFBSSxDQUFDO2dCQUFFTSxhQUFhLENBQUM7WUFBRSxHQUN2QkgsS0FBSyxDQUFDLEdBQ05DLE1BQU0sQ0FBQyx5Q0FDUEMsSUFBSTtZQUVQLGlCQUFpQjtZQUNqQnRFLHFEQUFTQSxDQUFDK0QsSUFBSSxDQUFDO2dCQUNiQyxRQUFRO2dCQUNSTyxhQUFhO29CQUNYQyxNQUFNZDtnQkFDUjtZQUNGLEdBQ0dPLElBQUksQ0FBQztnQkFBRU0sYUFBYSxDQUFDO1lBQUUsR0FDdkJILEtBQUssQ0FBQyxHQUNOQyxNQUFNLENBQUMseUNBQ1BDLElBQUk7U0FDUjtRQUVELHlCQUF5QjtRQUN6QixNQUFNSSxpQkFBaUI7WUFDckJoRSxlQUFlZ0IsS0FBS2lELEtBQUssQ0FBQ2pELEtBQUtDLFNBQVMsQ0FBQ2pCO1lBQ3pDQyxZQUFZZSxLQUFLaUQsS0FBSyxDQUFDakQsS0FBS0MsU0FBUyxDQUFDaEI7WUFDdENDLGFBQWFjLEtBQUtpRCxLQUFLLENBQUNqRCxLQUFLQyxTQUFTLENBQUNmO1FBQ3pDO1FBRUEsT0FBTztZQUNMZ0UsT0FBT0Y7WUFDUCxhQUFhO1lBQ2JHLFlBQVk7UUFDZDtJQUVGLEVBQUUsT0FBT2hFLE9BQU87UUFDZGlFLFFBQVFqRSxLQUFLLENBQUMsaUNBQWlDQTtRQUUvQyxzQkFBc0I7UUFDdEIsT0FBTztZQUNMK0QsT0FBTztnQkFDTGxFLGVBQWUsRUFBRTtnQkFDakJDLFlBQVksRUFBRTtnQkFDZEMsYUFBYSxFQUFFO1lBQ2pCO1lBQ0EsZUFBZTtZQUNmaUUsWUFBWTtRQUNkO0lBQ0Y7QUFDRixFQUFFIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvcGFnZXMvaW5kZXgudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyBHZXRTdGF0aWNQcm9wcyB9IGZyb20gJ25leHQnO1xuaW1wb3J0IExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvTGF5b3V0JztcbmltcG9ydCBUb29sQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvVG9vbENhcmQnO1xuaW1wb3J0IENhdGVnb3J5Q2FyZCBmcm9tICdAL2NvbXBvbmVudHMvQ2F0ZWdvcnlDYXJkJztcbmltcG9ydCBFcnJvck1lc3NhZ2UgZnJvbSAnQC9jb21wb25lbnRzL0Vycm9yTWVzc2FnZSc7XG5pbXBvcnQgTG9naW5Nb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvYXV0aC9Mb2dpbk1vZGFsJztcbmltcG9ydCB7IFRvb2wgfSBmcm9tICdAL2xpYi9hcGknO1xuaW1wb3J0IHsgZ2V0V2Vic2l0ZVN0cnVjdHVyZWREYXRhLCBnZXRPcmdhbml6YXRpb25TdHJ1Y3R1cmVkRGF0YSwgZ2V0VG9vbExpc3RTdHJ1Y3R1cmVkRGF0YSB9IGZyb20gJ0AvbGliL3Nlby9zdHJ1Y3R1cmVkRGF0YSc7XG5pbXBvcnQgeyBTZWFyY2gsIFRyZW5kaW5nVXAsIFN0YXIsIFphcCwgQ2FsZW5kYXIsIENsb2NrIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCBkYkNvbm5lY3QgZnJvbSAnQC9saWIvbW9uZ29kYic7XG5pbXBvcnQgVG9vbE1vZGVsIGZyb20gJ0AvbW9kZWxzL1Rvb2wnO1xuXG4vLyDliIbnsbvmlbDmja5cbmNvbnN0IGNhdGVnb3JpZXMgPSBbXG4gIHtcbiAgICBfaWQ6ICcxJyxcbiAgICBuYW1lOiAn5paH5pys55Sf5oiQJyxcbiAgICBzbHVnOiAndGV4dC1nZW5lcmF0aW9uJyxcbiAgICBkZXNjcmlwdGlvbjogJ0FJIHRvb2xzIGZvciBnZW5lcmF0aW5nIGFuZCBlZGl0aW5nIHRleHQgY29udGVudCcsXG4gICAgaWNvbjogJ/Cfk50nLFxuICAgIGNvbG9yOiAnIzNCODJGNicsXG4gICAgdG9vbENvdW50OiAyNVxuICB9LFxuICB7XG4gICAgX2lkOiAnMicsXG4gICAgbmFtZTogJ+WbvuWDj+eUn+aIkCcsXG4gICAgc2x1ZzogJ2ltYWdlLWdlbmVyYXRpb24nLFxuICAgIGRlc2NyaXB0aW9uOiAnQUkgdG9vbHMgZm9yIGNyZWF0aW5nIGFuZCBlZGl0aW5nIGltYWdlcycsXG4gICAgaWNvbjogJ/CfjqgnLFxuICAgIGNvbG9yOiAnIzhCNUNGNicsXG4gICAgdG9vbENvdW50OiAxOFxuICB9LFxuICB7XG4gICAgX2lkOiAnMycsXG4gICAgbmFtZTogJ+S7o+eggeeUn+aIkCcsXG4gICAgc2x1ZzogJ2NvZGUtZ2VuZXJhdGlvbicsXG4gICAgZGVzY3JpcHRpb246ICdBSSB0b29scyBmb3Igd3JpdGluZyBhbmQgZGVidWdnaW5nIGNvZGUnLFxuICAgIGljb246ICfwn5K7JyxcbiAgICBjb2xvcjogJyNGNTlFMEInLFxuICAgIHRvb2xDb3VudDogMTJcbiAgfSxcbiAge1xuICAgIF9pZDogJzQnLFxuICAgIG5hbWU6ICfmlbDmja7liIbmnpAnLFxuICAgIHNsdWc6ICdkYXRhLWFuYWx5c2lzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0FJIHRvb2xzIGZvciBhbmFseXppbmcgYW5kIHZpc3VhbGl6aW5nIGRhdGEnLFxuICAgIGljb246ICfwn5OKJyxcbiAgICBjb2xvcjogJyMwNkI2RDQnLFxuICAgIHRvb2xDb3VudDogMTVcbiAgfVxuXTtcblxuaW50ZXJmYWNlIEhvbWVQcm9wcyB7XG4gIGZlYXR1cmVkVG9vbHM6IFRvb2xbXTtcbiAgdG9kYXlUb29sczogVG9vbFtdO1xuICByZWNlbnRUb29sczogVG9vbFtdO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKHsgZmVhdHVyZWRUb29scywgdG9kYXlUb29scywgcmVjZW50VG9vbHMgfTogSG9tZVByb3BzKSB7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNMb2dpbk1vZGFsT3Blbiwgc2V0SXNMb2dpbk1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g55Sf5oiQ57uT5p6E5YyW5pWw5o2uXG4gIGNvbnN0IHdlYnNpdGVTdHJ1Y3R1cmVkRGF0YSA9IGdldFdlYnNpdGVTdHJ1Y3R1cmVkRGF0YSgpO1xuICBjb25zdCBvcmdhbml6YXRpb25TdHJ1Y3R1cmVkRGF0YSA9IGdldE9yZ2FuaXphdGlvblN0cnVjdHVyZWREYXRhKCk7XG4gIGNvbnN0IGFsbFRvb2xzID0gWy4uLmZlYXR1cmVkVG9vbHMsIC4uLnRvZGF5VG9vbHMsIC4uLnJlY2VudFRvb2xzXTtcbiAgY29uc3QgdG9vbExpc3RTdHJ1Y3R1cmVkRGF0YSA9IGFsbFRvb2xzLmxlbmd0aCA+IDAgPyBnZXRUb29sTGlzdFN0cnVjdHVyZWREYXRhKGFsbFRvb2xzKSA6IG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0PlxuICAgICAgey8qIOe7k+aehOWMluaVsOaNriAqL31cbiAgICAgIDxzY3JpcHRcbiAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxuICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgIF9faHRtbDogSlNPTi5zdHJpbmdpZnkod2Vic2l0ZVN0cnVjdHVyZWREYXRhKVxuICAgICAgICB9fVxuICAgICAgLz5cbiAgICAgIDxzY3JpcHRcbiAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxuICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgIF9faHRtbDogSlNPTi5zdHJpbmdpZnkob3JnYW5pemF0aW9uU3RydWN0dXJlZERhdGEpXG4gICAgICAgIH19XG4gICAgICAvPlxuICAgICAge3Rvb2xMaXN0U3RydWN0dXJlZERhdGEgJiYgKFxuICAgICAgICA8c2NyaXB0XG4gICAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxuICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XG4gICAgICAgICAgICBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KHRvb2xMaXN0U3RydWN0dXJlZERhdGEpXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBFcnJvciBNZXNzYWdlICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOCBwdC04XCI+XG4gICAgICAgICAgPEVycm9yTWVzc2FnZVxuICAgICAgICAgICAgbWVzc2FnZT17ZXJyb3J9XG4gICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRFcnJvcignJyl9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby0xMDAgcHktMjBcIiByb2xlPVwiYmFubmVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgbWQ6dGV4dC02eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNlwiPlxuICAgICAgICAgICAgICDlj5HnjrDmnIDlpb3nmoRcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPiBBSSDlt6Xlhbc8L3NwYW4+XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwIG1iLTggbWF4LXctM3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAg5o6i57Si57K+6YCJ55qE5Lq65bel5pm66IO95bel5YW36ZuG5ZCI77yM5o+Q5Y2H5oKo55qE5bel5L2c5pWI546H5ZKM5Yib6YCg5Yqb44CC5LuO5paH5pys55Sf5oiQ5Yiw5Zu+5YOP5Yib5L2c77yM5om+5Yiw6YCC5ZCI5oKo6ZyA5rGC55qE5a6M576O5bel5YW344CCXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIHsvKiBTZWFyY2ggQmFyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy0yeGwgbXgtYXV0byBtYi04XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5pCc57SiIEFJIOW3peWFt+OAgeWIhuexu+aIluWKn+iDvS4uLlwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcGwtMTIgcHItNCBweS00IHRleHQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLXhsIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCBzaGFkb3ctbGdcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTQgdG9wLTQgaC02IHctNiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENUQSBCdXR0b25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00IGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIi90b29sc1wiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtYmFzZSBmb250LW1lZGl1bSByb3VuZGVkLWxnIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJtci0yIGgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICAgIOa1j+iniOaJgOacieW3peWFt1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9zdWJtaXRcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC04IHB5LTMgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gcm91bmRlZC1sZyB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5o+Q5Lqk5oKo55qE5bel5YW3XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvaGVhZGVyPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIFRvZGF5J3MgVG9vbHMgU2VjdGlvbiAqL31cbiAgICAgIHt0b2RheVRvb2xzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAgdG8tYmx1ZS01MFwiIGFyaWEtbGFiZWxsZWRieT1cInRvZGF5LXRvb2xzLWhlYWRpbmdcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICAgIDxoMiBpZD1cInRvZGF5LXRvb2xzLWhlYWRpbmdcIiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBtci0yIGgtOCB3LTggdGV4dC1ncmVlbi02MDBcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxuICAgICAgICAgICAgICAgIOS7iuaXpeWPkeW4g1xuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICDku4rlpKnliJrliJrlj5HluIPnmoTmnIDmlrAgQUkg5bel5YW3XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIiByb2xlPVwibGlzdFwiIGFyaWEtbGFiZWw9XCLku4rml6Xlj5HluIPnmoRBSeW3peWFt1wiPlxuICAgICAgICAgICAgICB7dG9kYXlUb29scy5tYXAoKHRvb2wpID0+IChcbiAgICAgICAgICAgICAgICA8YXJ0aWNsZSBrZXk9e3Rvb2wuX2lkfSByb2xlPVwibGlzdGl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgIDxUb29sQ2FyZFxuICAgICAgICAgICAgICAgICAgICB0b29sPXt0b29sfVxuICAgICAgICAgICAgICAgICAgICBvbkxvZ2luUmVxdWlyZWQ9eygpID0+IHNldElzTG9naW5Nb2RhbE9wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvYXJ0aWNsZT5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuICAgICAgKX1cblxuICAgICAgey8qIFJlY2VudCBUb29scyBTZWN0aW9uICovfVxuICAgICAge3JlY2VudFRvb2xzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy13aGl0ZVwiIGFyaWEtbGFiZWxsZWRieT1cInJlY2VudC10b29scy1oZWFkaW5nXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgICAgICA8aDIgaWQ9XCJyZWNlbnQtdG9vbHMtaGVhZGluZ1wiIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIG1yLTIgaC04IHctOCB0ZXh0LWJsdWUtNjAwXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cbiAgICAgICAgICAgICAgICDmnIDov5Hlj5HluINcbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAg6L+H5Y675LiA5ZGo5YaF5Y+R5biD55qE5pawIEFJIOW3peWFt1xuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgIHtyZWNlbnRUb29scy5tYXAoKHRvb2wpID0+IChcbiAgICAgICAgICAgICAgICA8VG9vbENhcmRcbiAgICAgICAgICAgICAgICAgIGtleT17dG9vbC5faWR9XG4gICAgICAgICAgICAgICAgICB0b29sPXt0b29sfVxuICAgICAgICAgICAgICAgICAgb25Mb2dpblJlcXVpcmVkPXsoKSA9PiBzZXRJc0xvZ2luTW9kYWxPcGVuKHRydWUpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtOFwiPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9XCIvdG9vbHM/c29ydD1wdWJsaXNoZWRBdCZvcmRlcj1kZXNjXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNiBweS0zIGJvcmRlciBib3JkZXItYmx1ZS02MDAgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIHJvdW5kZWQtbGcgdGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOafpeeci+abtOWkmuacgOaWsOW3peWFt1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuICAgICAgKX1cblxuICAgICAgey8qIEZlYXR1cmVkIFRvb2xzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBiZy1ncmF5LTUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgbXItMiBoLTggdy04IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICDng63pl6jlt6XlhbdcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAg5pyA5Y+X5qyi6L+O5ZKM6K+E5Lu35pyA6auY55qEIEFJIOW3peWFt1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICB7ZmVhdHVyZWRUb29scy5tYXAoKHRvb2wpID0+IChcbiAgICAgICAgICAgICAgPFRvb2xDYXJkXG4gICAgICAgICAgICAgICAga2V5PXt0b29sLl9pZH1cbiAgICAgICAgICAgICAgICB0b29sPXt0b29sfVxuICAgICAgICAgICAgICAgIG9uTG9naW5SZXF1aXJlZD17KCkgPT4gc2V0SXNMb2dpbk1vZGFsT3Blbih0cnVlKX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtdC04XCI+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL3Rvb2xzXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTYgcHktMyBib3JkZXIgYm9yZGVyLWJsdWUtNjAwIHRleHQtYmFzZSBmb250LW1lZGl1bSByb3VuZGVkLWxnIHRleHQtYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOafpeeci+abtOWkmuW3peWFt1xuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIENhdGVnb3JpZXMgU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgbXItMiBoLTggdy04IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgICAgICDng63pl6jliIbnsbtcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAg5oyJ5Yqf6IO95YiG57G75rWP6KeIIEFJIOW3peWFt1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC02XCI+XG4gICAgICAgICAgICB7Y2F0ZWdvcmllcy5tYXAoKGNhdGVnb3J5KSA9PiAoXG4gICAgICAgICAgICAgIDxDYXRlZ29yeUNhcmQga2V5PXtjYXRlZ29yeS5faWR9IGNhdGVnb3J5PXtjYXRlZ29yeX0gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtdC04XCI+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL2NhdGVnb3JpZXNcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNiBweS0zIGJvcmRlciBib3JkZXItYmx1ZS02MDAgdGV4dC1iYXNlIGZvbnQtbWVkaXVtIHJvdW5kZWQtbGcgdGV4dC1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5p+l55yL5omA5pyJ5YiG57G7XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogU3RhdHMgU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTE2IGJnLWJsdWUtNjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMlwiPjUwMCs8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMTAwXCI+QUkg5bel5YW3PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMlwiPjUwKzwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDBcIj7lt6XlhbfliIbnsbs8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+MTBLKzwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmx1ZS0xMDBcIj7nlKjmiLforr/pl648L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIExvZ2luIE1vZGFsICovfVxuICAgICAgPExvZ2luTW9kYWxcbiAgICAgICAgaXNPcGVuPXtpc0xvZ2luTW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc0xvZ2luTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgIC8+XG4gICAgPC9MYXlvdXQ+XG4gICk7XG59XG5cbi8vIOacjeWKoeerr+aVsOaNruiOt+WPluWHveaVsFxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzOiBHZXRTdGF0aWNQcm9wczxIb21lUHJvcHM+ID0gYXN5bmMgKCkgPT4ge1xuICB0cnkge1xuICAgIGF3YWl0IGRiQ29ubmVjdCgpO1xuXG4gICAgLy8g5YeG5aSH5pel5pyf5Y+C5pWwXG4gICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpO1xuICAgIGNvbnN0IHRvZGF5U3RhcnQgPSBuZXcgRGF0ZSh0b2RheSk7XG4gICAgdG9kYXlTdGFydC5zZXRIb3VycygwLCAwLCAwLCAwKTtcbiAgICBjb25zdCB0b2RheUVuZCA9IG5ldyBEYXRlKHRvZGF5KTtcbiAgICB0b2RheUVuZC5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpO1xuXG4gICAgY29uc3Qgc2V2ZW5EYXlzQWdvID0gbmV3IERhdGUoKTtcbiAgICBzZXZlbkRheXNBZ28uc2V0RGF0ZShzZXZlbkRheXNBZ28uZ2V0RGF0ZSgpIC0gNyk7XG4gICAgc2V2ZW5EYXlzQWdvLnNldEhvdXJzKDAsIDAsIDAsIDApO1xuXG4gICAgLy8g5bm26KGM6I635Y+W5omA5pyJ5pWw5o2uXG4gICAgY29uc3QgW2ZlYXR1cmVkVG9vbHMsIHRvZGF5VG9vbHMsIHJlY2VudFRvb2xzXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgIC8vIOeDremXqOW3peWFtyAtIOaMiea1j+iniOmHj+aOkuW6j1xuICAgICAgVG9vbE1vZGVsLmZpbmQoeyBzdGF0dXM6ICdwdWJsaXNoZWQnIH0pXG4gICAgICAgIC5zb3J0KHsgdmlld3M6IC0xLCBsaWtlczogLTEgfSlcbiAgICAgICAgLmxpbWl0KDYpXG4gICAgICAgIC5zZWxlY3QoJy1zdWJtaXR0ZWRCeSAtcmV2aWV3Tm90ZXMgLXJldmlld2VkQnknKVxuICAgICAgICAubGVhbigpLFxuXG4gICAgICAvLyDlvZPml6Xlj5HluIPnmoTlt6XlhbdcbiAgICAgIFRvb2xNb2RlbC5maW5kKHtcbiAgICAgICAgc3RhdHVzOiAncHVibGlzaGVkJyxcbiAgICAgICAgcHVibGlzaGVkQXQ6IHtcbiAgICAgICAgICAkZ3RlOiB0b2RheVN0YXJ0LFxuICAgICAgICAgICRsdGU6IHRvZGF5RW5kXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgICAgIC5zb3J0KHsgcHVibGlzaGVkQXQ6IC0xIH0pXG4gICAgICAgIC5saW1pdCg2KVxuICAgICAgICAuc2VsZWN0KCctc3VibWl0dGVkQnkgLXJldmlld05vdGVzIC1yZXZpZXdlZEJ5JylcbiAgICAgICAgLmxlYW4oKSxcblxuICAgICAgLy8g5pyA6L+R5Y+R5biD55qE5bel5YW3IC0g6L+H5Y675LiA5ZGoXG4gICAgICBUb29sTW9kZWwuZmluZCh7XG4gICAgICAgIHN0YXR1czogJ3B1Ymxpc2hlZCcsXG4gICAgICAgIHB1Ymxpc2hlZEF0OiB7XG4gICAgICAgICAgJGd0ZTogc2V2ZW5EYXlzQWdvXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgICAgIC5zb3J0KHsgcHVibGlzaGVkQXQ6IC0xIH0pXG4gICAgICAgIC5saW1pdCg2KVxuICAgICAgICAuc2VsZWN0KCctc3VibWl0dGVkQnkgLXJldmlld05vdGVzIC1yZXZpZXdlZEJ5JylcbiAgICAgICAgLmxlYW4oKVxuICAgIF0pO1xuXG4gICAgLy8g5bqP5YiX5YyW5pWw5o2u5Lul6YG/5YWNIE5leHQuanMg5bqP5YiX5YyW6Zeu6aKYXG4gICAgY29uc3Qgc2VyaWFsaXplZERhdGEgPSB7XG4gICAgICBmZWF0dXJlZFRvb2xzOiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KGZlYXR1cmVkVG9vbHMpKSxcbiAgICAgIHRvZGF5VG9vbHM6IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodG9kYXlUb29scykpLFxuICAgICAgcmVjZW50VG9vbHM6IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkocmVjZW50VG9vbHMpKVxuICAgIH07XG5cbiAgICByZXR1cm4ge1xuICAgICAgcHJvcHM6IHNlcmlhbGl6ZWREYXRhLFxuICAgICAgLy8g6YeN5paw6aqM6K+B5pe26Ze077yaNeWIhumSn1xuICAgICAgcmV2YWxpZGF0ZTogMzAwXG4gICAgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGhvbWVwYWdlIGRhdGE6JywgZXJyb3IpO1xuXG4gICAgLy8g6L+U5Zue56m65pWw5o2u6ICM5LiN5piv5aSx6LSl77yM56Gu5L+d6aG16Z2i6IO95aSf5riy5p+TXG4gICAgcmV0dXJuIHtcbiAgICAgIHByb3BzOiB7XG4gICAgICAgIGZlYXR1cmVkVG9vbHM6IFtdLFxuICAgICAgICB0b2RheVRvb2xzOiBbXSxcbiAgICAgICAgcmVjZW50VG9vbHM6IFtdXG4gICAgICB9LFxuICAgICAgLy8g5Ye66ZSZ5pe25pu055+t55qE6YeN5paw6aqM6K+B5pe26Ze0XG4gICAgICByZXZhbGlkYXRlOiA2MFxuICAgIH07XG4gIH1cbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkxpbmsiLCJMYXlvdXQiLCJUb29sQ2FyZCIsIkNhdGVnb3J5Q2FyZCIsIkVycm9yTWVzc2FnZSIsIkxvZ2luTW9kYWwiLCJnZXRXZWJzaXRlU3RydWN0dXJlZERhdGEiLCJnZXRPcmdhbml6YXRpb25TdHJ1Y3R1cmVkRGF0YSIsImdldFRvb2xMaXN0U3RydWN0dXJlZERhdGEiLCJTZWFyY2giLCJUcmVuZGluZ1VwIiwiU3RhciIsIlphcCIsIkNhbGVuZGFyIiwiQ2xvY2siLCJkYkNvbm5lY3QiLCJUb29sTW9kZWwiLCJjYXRlZ29yaWVzIiwiX2lkIiwibmFtZSIsInNsdWciLCJkZXNjcmlwdGlvbiIsImljb24iLCJjb2xvciIsInRvb2xDb3VudCIsIkhvbWUiLCJmZWF0dXJlZFRvb2xzIiwidG9kYXlUb29scyIsInJlY2VudFRvb2xzIiwiZXJyb3IiLCJzZXRFcnJvciIsImlzTG9naW5Nb2RhbE9wZW4iLCJzZXRJc0xvZ2luTW9kYWxPcGVuIiwid2Vic2l0ZVN0cnVjdHVyZWREYXRhIiwib3JnYW5pemF0aW9uU3RydWN0dXJlZERhdGEiLCJhbGxUb29scyIsInRvb2xMaXN0U3RydWN0dXJlZERhdGEiLCJsZW5ndGgiLCJzY3JpcHQiLCJ0eXBlIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJKU09OIiwic3RyaW5naWZ5IiwiZGl2IiwiY2xhc3NOYW1lIiwibWVzc2FnZSIsIm9uQ2xvc2UiLCJzZWN0aW9uIiwicm9sZSIsImhlYWRlciIsImgxIiwic3BhbiIsInAiLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwiaHJlZiIsImFyaWEtbGFiZWxsZWRieSIsImgyIiwiaWQiLCJhcmlhLWhpZGRlbiIsImFyaWEtbGFiZWwiLCJtYXAiLCJ0b29sIiwiYXJ0aWNsZSIsIm9uTG9naW5SZXF1aXJlZCIsImNhdGVnb3J5IiwiaXNPcGVuIiwiZ2V0U3RhdGljUHJvcHMiLCJ0b2RheSIsIkRhdGUiLCJ0b2RheVN0YXJ0Iiwic2V0SG91cnMiLCJ0b2RheUVuZCIsInNldmVuRGF5c0FnbyIsInNldERhdGUiLCJnZXREYXRlIiwiUHJvbWlzZSIsImFsbCIsImZpbmQiLCJzdGF0dXMiLCJzb3J0Iiwidmlld3MiLCJsaWtlcyIsImxpbWl0Iiwic2VsZWN0IiwibGVhbiIsInB1Ymxpc2hlZEF0IiwiJGd0ZSIsIiRsdGUiLCJzZXJpYWxpemVkRGF0YSIsInBhcnNlIiwicHJvcHMiLCJyZXZhbGlkYXRlIiwiY29uc29sZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/components/CategoryCard.tsx":
/*!*****************************************!*\
  !*** ./src/components/CategoryCard.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst CategoryCard = ({ category })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: `/categories/${category.slug}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 rounded-lg flex items-center justify-center text-2xl\",\n                                style: {\n                                    backgroundColor: category.color || '#3B82F6'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white\",\n                                    children: category.icon || '🔧'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: [\n                                            category.toolCount,\n                                            \" 个工具\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm\",\n                        children: category.description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategoryCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/CategoryCard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ErrorMessage.tsx":
/*!*****************************************!*\
  !*** ./src/components/ErrorMessage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ErrorMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,X!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,X!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ErrorMessage({ message, onClose, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-red-50 border border-red-200 rounded-lg p-4 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-800 text-sm\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"ml-3 text-red-400 hover:text-red-600 transition-colors\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ErrorMessage.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_seo_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seo/PerformanceMonitor */ \"(pages-dir-node)/./src/components/seo/PerformanceMonitor.tsx\");\n\n\n\n\nconst Layout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_PerformanceMonitor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-sm\",\n                                                        children: \"AI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-gray-900\",\n                                                    children: \"AI Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 28,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"发现最新最好的 AI 工具，提升您的工作效率和创造力。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\",\n                                            children: \"快速链接\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/tools\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"工具目录\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/categories\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"分类浏览\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 46,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/submit\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"提交工具\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\",\n                                            children: \"支持\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"帮助中心\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"联系我们\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-gray-600 hover:text-blue-600\",\n                                                        children: \"隐私政策\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 mt-8 pt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-gray-600\",\n                                children: \"\\xa9 2024 AI Tools Directory. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/Layout.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ToolCard.tsx":
/*!*************************************!*\
  !*** ./src/components/ToolCard.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Eye,Heart!=!lucide-react */ \"(pages-dir-node)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _tools_LikeButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tools/LikeButton */ \"(pages-dir-node)/./src/components/tools/LikeButton.tsx\");\n/* harmony import */ var _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/OptimizedImage */ \"(pages-dir-node)/./src/components/ui/OptimizedImage.tsx\");\n/* harmony import */ var _constants_pricing__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/pricing */ \"(pages-dir-node)/./src/constants/pricing.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst ToolCard = ({ tool, onLoginRequired, onUnlike, isInLikedPage = false })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                tool.logo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    src: tool.logo,\n                                    alt: `${tool.name} logo`,\n                                    width: _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__.ImageSizes.toolLogo.width,\n                                    height: _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__.ImageSizes.toolLogo.height,\n                                    className: \"rounded-lg object-cover\",\n                                    sizes: _ui_OptimizedImage__WEBPACK_IMPORTED_MODULE_4__.ResponsiveSizes.toolLogo,\n                                    placeholder: \"blur\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-lg\",\n                                        children: tool.name.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: tool.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,_constants_pricing__WEBPACK_IMPORTED_MODULE_5__.getToolPricingColor)(tool.pricing)}`,\n                                            children: (0,_constants_pricing__WEBPACK_IMPORTED_MODULE_5__.getToolPricingText)(tool.pricing)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: tool.website,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-gray-400 hover:text-blue-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-sm mb-4 line-clamp-2\",\n                    children: tool.description\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-4\",\n                    children: [\n                        tool.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\",\n                                children: tag\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined)),\n                        tool.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700\",\n                            children: [\n                                \"+\",\n                                tool.tags.length - 3\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tool.views\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Eye_Heart_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: tool.likes\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tools_LikeButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    toolId: tool._id,\n                                    initialLikes: tool.likes,\n                                    onLoginRequired: onLoginRequired,\n                                    onUnlike: onUnlike,\n                                    isInLikedPage: isInLikedPage\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/tools/${tool._id}`,\n                                    className: \"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors\",\n                                    children: \"查看详情\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToolCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ToolCard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/auth/LoginModal.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/LoginModal.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaEnvelope,FaGithub,FaGoogle,FaTimes!=!react-icons/fa */ \"(pages-dir-node)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LoginModal({ isOpen, onClose }) {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('method');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationToken, setVerificationToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailError, setEmailError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const showToast = (message, type = 'success')=>{\n        // Simple toast implementation\n        const toast = document.createElement('div');\n        toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;\n        toast.textContent = message;\n        document.body.appendChild(toast);\n        setTimeout(()=>document.body.removeChild(toast), 3000);\n    };\n    const handleClose = ()=>{\n        setStep('method');\n        setEmail('');\n        setVerificationToken('');\n        setEmailError('');\n        onClose();\n    };\n    const handleOAuthLogin = async (provider)=>{\n        try {\n            setIsLoading(true);\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)(provider, {\n                callbackUrl: '/'\n            });\n        } catch (error) {\n            showToast('登录失败，请稍后重试', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEmailSubmit = async ()=>{\n        if (!email) {\n            setEmailError('请输入邮箱地址');\n            return;\n        }\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            setEmailError('请输入有效的邮箱地址');\n            return;\n        }\n        setEmailError('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/auth/send-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setVerificationToken(data.token);\n                setStep('code');\n                showToast('验证码已发送，请查看您的邮箱');\n            } else {\n                showToast(data.error || '发送失败，请稍后重试', 'error');\n            }\n        } catch (error) {\n            showToast('网络错误，请检查网络连接', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCodeVerify = async (code)=>{\n        if (code.length !== 6) return;\n        setIsLoading(true);\n        try {\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)('email-code', {\n                email,\n                code,\n                token: verificationToken,\n                redirect: false\n            });\n            if (result?.ok) {\n                showToast('登录成功，欢迎回来！');\n                handleClose();\n            // NextAuth会自动更新session，不需要手动刷新页面\n            } else {\n                showToast(result?.error || '验证码错误', 'error');\n            }\n        } catch (error) {\n            showToast('网络错误，请检查网络连接', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderMethodStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: \"选择登录方式\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50\",\n                            onClick: ()=>handleOAuthLogin('google'),\n                            disabled: isLoading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGoogle, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 11\n                                }, this),\n                                \"使用 Google 登录\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                            onClick: ()=>handleOAuthLogin('github'),\n                            disabled: isLoading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGithub, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 11\n                                }, this),\n                                \"使用 GitHub 登录\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-center text-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 bg-white text-gray-500\",\n                                children: \"或\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors\",\n                    onClick: ()=>setStep('email'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaEnvelope, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, this),\n                        \"使用邮箱登录\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n            lineNumber: 119,\n            columnNumber: 5\n        }, this);\n    const renderEmailStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: \"输入您的邮箱地址，我们将发送验证码\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"邮箱地址\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: email,\n                            onChange: (e)=>setEmail(e.target.value),\n                            placeholder: \"请输入邮箱地址\",\n                            onKeyPress: (e)=>e.key === 'Enter' && handleEmailSubmit(),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 9\n                        }, this),\n                        emailError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-red-600\",\n                            children: emailError\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                            onClick: handleEmailSubmit,\n                            disabled: isLoading,\n                            children: isLoading ? '发送中...' : '发送验证码'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                            onClick: ()=>setStep('method'),\n                            children: \"返回\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n            lineNumber: 164,\n            columnNumber: 5\n        }, this);\n    const handleCodeInputChange = (index, value)=>{\n        if (value.length > 1) return;\n        const inputs = document.querySelectorAll('.code-input');\n        inputs[index].value = value;\n        // Auto-focus next input\n        if (value && index < 5) {\n            inputs[index + 1]?.focus();\n        }\n        // Check if all inputs are filled\n        const code = Array.from(inputs).map((input)=>input.value).join('');\n        if (code.length === 6) {\n            handleCodeVerify(code);\n        }\n    };\n    const renderCodeStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: [\n                        \"请输入发送到 \",\n                        email,\n                        \" 的6位验证码\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"验证码\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-2\",\n                            children: [\n                                0,\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    maxLength: 1,\n                                    onChange: (e)=>handleCodeInputChange(index, e.target.value),\n                                    disabled: isLoading,\n                                    className: \"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50\"\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                            onClick: ()=>setStep('email'),\n                            children: \"重新发送验证码\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                            onClick: ()=>setStep('method'),\n                            children: \"返回\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n            lineNumber: 224,\n            columnNumber: 5\n        }, this);\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-50\",\n                onClick: handleClose\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 text-center flex-1\",\n                                children: [\n                                    step === 'method' && '登录 AI Tools Directory',\n                                    step === 'email' && '邮箱登录',\n                                    step === 'code' && '输入验证码'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTimes, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            step === 'method' && renderMethodStep(),\n                            step === 'email' && renderEmailStep(),\n                            step === 'code' && renderCodeStep()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/auth/LoginModal.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/seo/PerformanceMonitor.tsx":
/*!***************************************************!*\
  !*** ./src/components/seo/PerformanceMonitor.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerformanceOptimizations: () => (/* binding */ PerformanceOptimizations),\n/* harmony export */   \"default\": () => (/* binding */ PerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default,PerformanceOptimizations auto */ \nfunction PerformanceMonitor() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PerformanceMonitor.useEffect\": ()=>{\n            // 只在生产环境中启用性能监控\n            if (true) {\n                return;\n            }\n            const metrics = {};\n            // 监控 First Contentful Paint (FCP)\n            const observeFCP = {\n                \"PerformanceMonitor.useEffect.observeFCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFCP\": (list)=>{\n                            const entries = list.getEntries();\n                            const fcpEntry = entries.find({\n                                \"PerformanceMonitor.useEffect.observeFCP.fcpEntry\": (entry)=>entry.name === 'first-contentful-paint'\n                            }[\"PerformanceMonitor.useEffect.observeFCP.fcpEntry\"]);\n                            if (fcpEntry) {\n                                metrics.fcp = fcpEntry.startTime;\n                                reportMetric('FCP', fcpEntry.startTime);\n                            }\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFCP\"];\n            // 监控 Largest Contentful Paint (LCP)\n            const observeLCP = {\n                \"PerformanceMonitor.useEffect.observeLCP\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeLCP\": (list)=>{\n                            const entries = list.getEntries();\n                            const lastEntry = entries[entries.length - 1];\n                            metrics.lcp = lastEntry.startTime;\n                            reportMetric('LCP', lastEntry.startTime);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeLCP\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'largest-contentful-paint'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeLCP\"];\n            // 监控 First Input Delay (FID)\n            const observeFID = {\n                \"PerformanceMonitor.useEffect.observeFID\": ()=>{\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeFID\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeFID\": (entry)=>{\n                                    metrics.fid = entry.processingStart - entry.startTime;\n                                    reportMetric('FID', entry.processingStart - entry.startTime);\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeFID\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'first-input'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeFID\"];\n            // 监控 Cumulative Layout Shift (CLS)\n            const observeCLS = {\n                \"PerformanceMonitor.useEffect.observeCLS\": ()=>{\n                    let clsValue = 0;\n                    const observer = new PerformanceObserver({\n                        \"PerformanceMonitor.useEffect.observeCLS\": (list)=>{\n                            const entries = list.getEntries();\n                            entries.forEach({\n                                \"PerformanceMonitor.useEffect.observeCLS\": (entry)=>{\n                                    if (!entry.hadRecentInput) {\n                                        clsValue += entry.value;\n                                    }\n                                }\n                            }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                            metrics.cls = clsValue;\n                            reportMetric('CLS', clsValue);\n                        }\n                    }[\"PerformanceMonitor.useEffect.observeCLS\"]);\n                    observer.observe({\n                        entryTypes: [\n                            'layout-shift'\n                        ]\n                    });\n                }\n            }[\"PerformanceMonitor.useEffect.observeCLS\"];\n            // 监控 Time to First Byte (TTFB)\n            const observeTTFB = {\n                \"PerformanceMonitor.useEffect.observeTTFB\": ()=>{\n                    const navigationEntry = performance.getEntriesByType('navigation')[0];\n                    if (navigationEntry) {\n                        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n                        metrics.ttfb = ttfb;\n                        reportMetric('TTFB', ttfb);\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect.observeTTFB\"];\n            // 报告性能指标\n            const reportMetric = {\n                \"PerformanceMonitor.useEffect.reportMetric\": (name, value)=>{\n                    // 在开发环境中输出到控制台\n                    if (true) {\n                        console.log(`Performance Metric - ${name}:`, value);\n                    }\n                    // 在生产环境中可以发送到分析服务\n                    // 例如 Google Analytics, Vercel Analytics 等\n                    if (false) {}\n                }\n            }[\"PerformanceMonitor.useEffect.reportMetric\"];\n            // 检查浏览器支持\n            if (typeof PerformanceObserver !== 'undefined') {\n                observeFCP();\n                observeLCP();\n                observeFID();\n                observeCLS();\n            }\n            observeTTFB();\n            // 页面卸载时报告最终指标\n            const reportFinalMetrics = {\n                \"PerformanceMonitor.useEffect.reportFinalMetrics\": ()=>{\n                    if (Object.keys(metrics).length > 0) {\n                        // 可以发送到分析服务\n                        console.log('Final Performance Metrics:', metrics);\n                    }\n                }\n            }[\"PerformanceMonitor.useEffect.reportFinalMetrics\"];\n            window.addEventListener('beforeunload', reportFinalMetrics);\n            return ({\n                \"PerformanceMonitor.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', reportFinalMetrics);\n                }\n            })[\"PerformanceMonitor.useEffect\"];\n        }\n    }[\"PerformanceMonitor.useEffect\"], []);\n    return null; // 这是一个无UI的监控组件\n}\n// 性能优化建议\nconst PerformanceOptimizations = {\n    // FCP 优化建议\n    fcp: {\n        good: 1800,\n        needsImprovement: 3000,\n        suggestions: [\n            '减少服务器响应时间',\n            '消除阻塞渲染的资源',\n            '压缩CSS和JavaScript',\n            '使用CDN加速资源加载'\n        ]\n    },\n    // LCP 优化建议\n    lcp: {\n        good: 2500,\n        needsImprovement: 4000,\n        suggestions: [\n            '优化图片加载',\n            '预加载关键资源',\n            '减少JavaScript执行时间',\n            '使用服务端渲染'\n        ]\n    },\n    // FID 优化建议\n    fid: {\n        good: 100,\n        needsImprovement: 300,\n        suggestions: [\n            '减少JavaScript执行时间',\n            '分割长任务',\n            '使用Web Workers',\n            '延迟加载非关键JavaScript'\n        ]\n    },\n    // CLS 优化建议\n    cls: {\n        good: 0.1,\n        needsImprovement: 0.25,\n        suggestions: [\n            '为图片和视频设置尺寸属性',\n            '避免在现有内容上方插入内容',\n            '使用transform动画而非改变布局的动画',\n            '预留广告位空间'\n        ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/seo/PerformanceMonitor.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/tools/LikeButton.tsx":
/*!*********************************************!*\
  !*** ./src/components/tools/LikeButton.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LikeButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaHeart_FaRegHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaHeart,FaRegHeart!=!react-icons/fa */ \"(pages-dir-node)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LikeButton({ toolId, initialLikes = 0, initialLiked = false, onLoginRequired, onUnlike, isInLikedPage = false }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [liked, setLiked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLiked);\n    const [likes, setLikes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialLikes);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 获取点赞状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LikeButton.useEffect\": ()=>{\n            const fetchLikeStatus = {\n                \"LikeButton.useEffect.fetchLikeStatus\": async ()=>{\n                    try {\n                        const response = await fetch(`/api/tools/${toolId}/like`);\n                        if (response.ok) {\n                            const data = await response.json();\n                            if (data.success) {\n                                setLiked(data.data.liked);\n                                setLikes(data.data.likes);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch like status:', error);\n                    }\n                }\n            }[\"LikeButton.useEffect.fetchLikeStatus\"];\n            if (session) {\n                fetchLikeStatus();\n            }\n        }\n    }[\"LikeButton.useEffect\"], [\n        toolId,\n        session\n    ]);\n    const handleLike = async ()=>{\n        if (!session) {\n            onLoginRequired?.();\n            return;\n        }\n        if (isLoading) return;\n        setIsLoading(true);\n        try {\n            // 如果在liked页面，发送强制unlike请求\n            const requestBody = isInLikedPage ? {\n                forceUnlike: true\n            } : {};\n            const response = await fetch(`/api/tools/${toolId}/like`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    const newLikedState = data.data.liked;\n                    setLiked(newLikedState);\n                    setLikes(data.data.likes);\n                    // 如果用户取消了点赞，并且提供了onUnlike回调，则调用它\n                    if (!newLikedState && onUnlike) {\n                        onUnlike(toolId);\n                    }\n                }\n            } else {\n                const errorData = await response.json();\n                console.error('Like failed:', errorData.message);\n            }\n        } catch (error) {\n            console.error('Like request failed:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleLike,\n        disabled: isLoading,\n        className: `\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ${liked ? 'bg-red-50 text-red-600 hover:bg-red-100' : 'bg-gray-50 text-gray-600 hover:bg-gray-100'}\n        ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}\n        border border-gray-200 hover:border-gray-300\n      `,\n        children: [\n            liked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_FaRegHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaHeart, {\n                className: \"w-4 h-4 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaHeart_FaRegHeart_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaRegHeart, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium\",\n                children: likes > 0 ? likes : ''\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL3Rvb2xzL0xpa2VCdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUU0QztBQUNDO0FBQ1E7QUFXdEMsU0FBU0ssV0FBVyxFQUNqQ0MsTUFBTSxFQUNOQyxlQUFlLENBQUMsRUFDaEJDLGVBQWUsS0FBSyxFQUNwQkMsZUFBZSxFQUNmQyxRQUFRLEVBQ1JDLGdCQUFnQixLQUFLLEVBQ0w7SUFDaEIsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUUsR0FBR1gsMkRBQVVBO0lBQ3BDLE1BQU0sQ0FBQ1ksT0FBT0MsU0FBUyxHQUFHZiwrQ0FBUUEsQ0FBQ1E7SUFDbkMsTUFBTSxDQUFDUSxPQUFPQyxTQUFTLEdBQUdqQiwrQ0FBUUEsQ0FBQ087SUFDbkMsTUFBTSxDQUFDVyxXQUFXQyxhQUFhLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUUzQyxTQUFTO0lBQ1RDLGdEQUFTQTtnQ0FBQztZQUNSLE1BQU1tQjt3REFBa0I7b0JBQ3RCLElBQUk7d0JBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLENBQUMsV0FBVyxFQUFFaEIsT0FBTyxLQUFLLENBQUM7d0JBQ3hELElBQUllLFNBQVNFLEVBQUUsRUFBRTs0QkFDZixNQUFNWCxPQUFPLE1BQU1TLFNBQVNHLElBQUk7NEJBQ2hDLElBQUlaLEtBQUthLE9BQU8sRUFBRTtnQ0FDaEJWLFNBQVNILEtBQUtBLElBQUksQ0FBQ0UsS0FBSztnQ0FDeEJHLFNBQVNMLEtBQUtBLElBQUksQ0FBQ0ksS0FBSzs0QkFDMUI7d0JBQ0Y7b0JBQ0YsRUFBRSxPQUFPVSxPQUFPO3dCQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtvQkFDaEQ7Z0JBQ0Y7O1lBRUEsSUFBSWIsU0FBUztnQkFDWE87WUFDRjtRQUNGOytCQUFHO1FBQUNkO1FBQVFPO0tBQVE7SUFFcEIsTUFBTWUsYUFBYTtRQUNqQixJQUFJLENBQUNmLFNBQVM7WUFDWko7WUFDQTtRQUNGO1FBRUEsSUFBSVMsV0FBVztRQUVmQyxhQUFhO1FBRWIsSUFBSTtZQUNGLDBCQUEwQjtZQUMxQixNQUFNVSxjQUFjbEIsZ0JBQWdCO2dCQUFFbUIsYUFBYTtZQUFLLElBQUksQ0FBQztZQUU3RCxNQUFNVCxXQUFXLE1BQU1DLE1BQU0sQ0FBQyxXQUFXLEVBQUVoQixPQUFPLEtBQUssQ0FBQyxFQUFFO2dCQUN4RHlCLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDTjtZQUN2QjtZQUVBLElBQUlSLFNBQVNFLEVBQUUsRUFBRTtnQkFDZixNQUFNWCxPQUFPLE1BQU1TLFNBQVNHLElBQUk7Z0JBQ2hDLElBQUlaLEtBQUthLE9BQU8sRUFBRTtvQkFDaEIsTUFBTVcsZ0JBQWdCeEIsS0FBS0EsSUFBSSxDQUFDRSxLQUFLO29CQUNyQ0MsU0FBU3FCO29CQUNUbkIsU0FBU0wsS0FBS0EsSUFBSSxDQUFDSSxLQUFLO29CQUV4QixpQ0FBaUM7b0JBQ2pDLElBQUksQ0FBQ29CLGlCQUFpQjFCLFVBQVU7d0JBQzlCQSxTQUFTSjtvQkFDWDtnQkFDRjtZQUNGLE9BQU87Z0JBQ0wsTUFBTStCLFlBQVksTUFBTWhCLFNBQVNHLElBQUk7Z0JBQ3JDRyxRQUFRRCxLQUFLLENBQUMsZ0JBQWdCVyxVQUFVQyxPQUFPO1lBQ2pEO1FBQ0YsRUFBRSxPQUFPWixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3hDLFNBQVU7WUFDUlAsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ29CO1FBQ0NDLFNBQVNaO1FBQ1RhLFVBQVV2QjtRQUNWd0IsV0FBVyxDQUFDOztRQUVWLEVBQUU1QixRQUNFLDRDQUNBLDZDQUNIO1FBQ0QsRUFBRUksWUFBWSxrQ0FBa0Msa0JBQWtCOztNQUVwRSxDQUFDOztZQUVBSixzQkFDQyw4REFBQ1gsNkZBQU9BO2dCQUFDdUMsV0FBVTs7Ozs7cUNBRW5CLDhEQUFDdEMsZ0dBQVVBO2dCQUFDc0MsV0FBVTs7Ozs7OzBCQUV4Qiw4REFBQ0M7Z0JBQUtELFdBQVU7MEJBQ2IxQixRQUFRLElBQUlBLFFBQVE7Ozs7Ozs7Ozs7OztBQUk3QiIsInNvdXJjZXMiOlsiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL3NyYy9jb21wb25lbnRzL3Rvb2xzL0xpa2VCdXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuaW1wb3J0IHsgRmFIZWFydCwgRmFSZWdIZWFydCB9IGZyb20gJ3JlYWN0LWljb25zL2ZhJztcblxuaW50ZXJmYWNlIExpa2VCdXR0b25Qcm9wcyB7XG4gIHRvb2xJZDogc3RyaW5nO1xuICBpbml0aWFsTGlrZXM/OiBudW1iZXI7XG4gIGluaXRpYWxMaWtlZD86IGJvb2xlYW47XG4gIG9uTG9naW5SZXF1aXJlZD86ICgpID0+IHZvaWQ7XG4gIG9uVW5saWtlPzogKHRvb2xJZDogc3RyaW5nKSA9PiB2b2lkO1xuICBpc0luTGlrZWRQYWdlPzogYm9vbGVhbjsgLy8g5paw5aKe77ya5qCH6K+G5piv5ZCm5ZyobGlrZWTpobXpnaJcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGlrZUJ1dHRvbih7XG4gIHRvb2xJZCxcbiAgaW5pdGlhbExpa2VzID0gMCxcbiAgaW5pdGlhbExpa2VkID0gZmFsc2UsXG4gIG9uTG9naW5SZXF1aXJlZCxcbiAgb25Vbmxpa2UsXG4gIGlzSW5MaWtlZFBhZ2UgPSBmYWxzZVxufTogTGlrZUJ1dHRvblByb3BzKSB7XG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCBbbGlrZWQsIHNldExpa2VkXSA9IHVzZVN0YXRlKGluaXRpYWxMaWtlZCk7XG4gIGNvbnN0IFtsaWtlcywgc2V0TGlrZXNdID0gdXNlU3RhdGUoaW5pdGlhbExpa2VzKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDojrflj5bngrnotZ7nirbmgIFcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBmZXRjaExpa2VTdGF0dXMgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3Rvb2xzLyR7dG9vbElkfS9saWtlYCk7XG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgaWYgKGRhdGEuc3VjY2Vzcykge1xuICAgICAgICAgICAgc2V0TGlrZWQoZGF0YS5kYXRhLmxpa2VkKTtcbiAgICAgICAgICAgIHNldExpa2VzKGRhdGEuZGF0YS5saWtlcyk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggbGlrZSBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpZiAoc2Vzc2lvbikge1xuICAgICAgZmV0Y2hMaWtlU3RhdHVzKCk7XG4gICAgfVxuICB9LCBbdG9vbElkLCBzZXNzaW9uXSk7XG5cbiAgY29uc3QgaGFuZGxlTGlrZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlc3Npb24pIHtcbiAgICAgIG9uTG9naW5SZXF1aXJlZD8uKCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKGlzTG9hZGluZykgcmV0dXJuO1xuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyDlpoLmnpzlnKhsaWtlZOmhtemdou+8jOWPkemAgeW8uuWItnVubGlrZeivt+axglxuICAgICAgY29uc3QgcmVxdWVzdEJvZHkgPSBpc0luTGlrZWRQYWdlID8geyBmb3JjZVVubGlrZTogdHJ1ZSB9IDoge307XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvdG9vbHMvJHt0b29sSWR9L2xpa2VgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdEJvZHkpLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBpZiAoZGF0YS5zdWNjZXNzKSB7XG4gICAgICAgICAgY29uc3QgbmV3TGlrZWRTdGF0ZSA9IGRhdGEuZGF0YS5saWtlZDtcbiAgICAgICAgICBzZXRMaWtlZChuZXdMaWtlZFN0YXRlKTtcbiAgICAgICAgICBzZXRMaWtlcyhkYXRhLmRhdGEubGlrZXMpO1xuXG4gICAgICAgICAgLy8g5aaC5p6c55So5oi35Y+W5raI5LqG54K56LWe77yM5bm25LiU5o+Q5L6b5LqGb25Vbmxpa2Xlm57osIPvvIzliJnosIPnlKjlroNcbiAgICAgICAgICBpZiAoIW5ld0xpa2VkU3RhdGUgJiYgb25Vbmxpa2UpIHtcbiAgICAgICAgICAgIG9uVW5saWtlKHRvb2xJZCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0xpa2UgZmFpbGVkOicsIGVycm9yRGF0YS5tZXNzYWdlKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTGlrZSByZXF1ZXN0IGZhaWxlZDonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGJ1dHRvblxuICAgICAgb25DbGljaz17aGFuZGxlTGlrZX1cbiAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtMyBweS0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXG4gICAgICAgICR7bGlrZWQgXG4gICAgICAgICAgPyAnYmctcmVkLTUwIHRleHQtcmVkLTYwMCBob3ZlcjpiZy1yZWQtMTAwJyBcbiAgICAgICAgICA6ICdiZy1ncmF5LTUwIHRleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS0xMDAnXG4gICAgICAgIH1cbiAgICAgICAgJHtpc0xvYWRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJ2hvdmVyOnNjYWxlLTEwNSd9XG4gICAgICAgIGJvcmRlciBib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwXG4gICAgICBgfVxuICAgID5cbiAgICAgIHtsaWtlZCA/IChcbiAgICAgICAgPEZhSGVhcnQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXJlZC01MDBcIiAvPlxuICAgICAgKSA6IChcbiAgICAgICAgPEZhUmVnSGVhcnQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICApfVxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICB7bGlrZXMgPiAwID8gbGlrZXMgOiAnJ31cbiAgICAgIDwvc3Bhbj5cbiAgICA8L2J1dHRvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVNlc3Npb24iLCJGYUhlYXJ0IiwiRmFSZWdIZWFydCIsIkxpa2VCdXR0b24iLCJ0b29sSWQiLCJpbml0aWFsTGlrZXMiLCJpbml0aWFsTGlrZWQiLCJvbkxvZ2luUmVxdWlyZWQiLCJvblVubGlrZSIsImlzSW5MaWtlZFBhZ2UiLCJkYXRhIiwic2Vzc2lvbiIsImxpa2VkIiwic2V0TGlrZWQiLCJsaWtlcyIsInNldExpa2VzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZmV0Y2hMaWtlU3RhdHVzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwianNvbiIsInN1Y2Nlc3MiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVMaWtlIiwicmVxdWVzdEJvZHkiLCJmb3JjZVVubGlrZSIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm5ld0xpa2VkU3RhdGUiLCJlcnJvckRhdGEiLCJtZXNzYWdlIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiY2xhc3NOYW1lIiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/tools/LikeButton.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ui/OptimizedImage.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/OptimizedImage.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageSizes: () => (/* binding */ ImageSizes),\n/* harmony export */   ResponsiveSizes: () => (/* binding */ ResponsiveSizes),\n/* harmony export */   \"default\": () => (/* binding */ OptimizedImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default,ImageSizes,ResponsiveSizes auto */ \n\n\nfunction OptimizedImage({ src, alt, width, height, className = '', priority = false, fill = false, sizes, placeholder = 'empty', blurDataURL, fallbackSrc = '/images/placeholder.svg', onError }) {\n    const [imgSrc, setImgSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(src);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleError = ()=>{\n        setHasError(true);\n        setIsLoading(false);\n        setImgSrc(fallbackSrc);\n        onError?.();\n    };\n    const handleLoad = ()=>{\n        setIsLoading(false);\n    };\n    // 生成模糊占位符\n    const generateBlurDataURL = (w = 10, h = 10)=>{\n        const canvas = document.createElement('canvas');\n        canvas.width = w;\n        canvas.height = h;\n        const ctx = canvas.getContext('2d');\n        if (ctx) {\n            ctx.fillStyle = '#f3f4f6';\n            ctx.fillRect(0, 0, w, h);\n        }\n        return canvas.toDataURL();\n    };\n    const imageProps = {\n        src: imgSrc,\n        alt,\n        className: `${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`,\n        onError: handleError,\n        onLoad: handleLoad,\n        priority,\n        placeholder: placeholder === 'blur' ? 'blur' : 'empty',\n        blurDataURL: blurDataURL || (placeholder === 'blur' ? generateBlurDataURL() : undefined),\n        sizes: sizes || (fill ? '100vw' : undefined)\n    };\n    if (fill) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    ...imageProps,\n                    fill: true,\n                    style: {\n                        objectFit: 'cover'\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gray-200 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                ...imageProps,\n                width: width,\n                height: height\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-200 animate-pulse\",\n                style: {\n                    width,\n                    height\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n// 预设的图片尺寸配置\nconst ImageSizes = {\n    avatar: {\n        width: 40,\n        height: 40\n    },\n    avatarLarge: {\n        width: 80,\n        height: 80\n    },\n    toolLogo: {\n        width: 64,\n        height: 64\n    },\n    toolLogoLarge: {\n        width: 128,\n        height: 128\n    },\n    thumbnail: {\n        width: 200,\n        height: 150\n    },\n    card: {\n        width: 300,\n        height: 200\n    },\n    hero: {\n        width: 1200,\n        height: 600\n    }\n};\n// 响应式图片尺寸字符串\nconst ResponsiveSizes = {\n    avatar: '40px',\n    toolLogo: '64px',\n    thumbnail: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n    card: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',\n    hero: '100vw',\n    full: '100vw'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ui/OptimizedImage.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/constants/pricing.ts":
/*!**********************************!*\
  !*** ./src/constants/pricing.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LAUNCH_OPTIONS: () => (/* binding */ LAUNCH_OPTIONS),\n/* harmony export */   PRICING_CONFIG: () => (/* binding */ PRICING_CONFIG),\n/* harmony export */   TOOL_PRICING_FORM_OPTIONS: () => (/* binding */ TOOL_PRICING_FORM_OPTIONS),\n/* harmony export */   TOOL_PRICING_OPTIONS: () => (/* binding */ TOOL_PRICING_OPTIONS),\n/* harmony export */   TOOL_PRICING_TYPES: () => (/* binding */ TOOL_PRICING_TYPES),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatStripeAmount: () => (/* binding */ formatStripeAmount),\n/* harmony export */   getPricingConfig: () => (/* binding */ getPricingConfig),\n/* harmony export */   getToolPricingColor: () => (/* binding */ getToolPricingColor),\n/* harmony export */   getToolPricingText: () => (/* binding */ getToolPricingText)\n/* harmony export */ });\n/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */ // 基础价格配置\nconst PRICING_CONFIG = {\n    // 优先发布服务价格\n    PRIORITY_LAUNCH: {\n        // 显示价格（元）\n        displayPrice: 19.9,\n        // Stripe价格（分为单位）\n        stripeAmount: 1990,\n        // 货币\n        currency: 'USD',\n        // Stripe货币代码（小写）\n        stripeCurrency: 'usd',\n        // 产品名称\n        productName: 'AI工具优先发布服务',\n        // 产品描述\n        description: '让您的AI工具获得优先审核和推荐位置',\n        // 功能特性\n        features: [\n            '可选择任意发布日期',\n            '优先审核处理',\n            '首页推荐位置',\n            '专属客服支持'\n        ]\n    },\n    // 免费发布配置\n    FREE_LAUNCH: {\n        displayPrice: 0,\n        stripeAmount: 0,\n        currency: 'USD',\n        stripeCurrency: 'usd',\n        productName: '免费发布服务',\n        description: '选择一个月后的任意发布日期',\n        features: [\n            '免费提交审核',\n            '发布日期：一个月后起',\n            '正常审核流程',\n            '标准展示位置'\n        ]\n    }\n};\n// 发布选项配置\nconst LAUNCH_OPTIONS = [\n    {\n        id: 'free',\n        title: '免费发布',\n        description: PRICING_CONFIG.FREE_LAUNCH.description,\n        price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n        features: PRICING_CONFIG.FREE_LAUNCH.features\n    },\n    {\n        id: 'paid',\n        title: '优先发布',\n        description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n        price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n        features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n        recommended: true\n    }\n];\n// 工具定价类型配置\nconst TOOL_PRICING_TYPES = {\n    FREE: {\n        value: 'free',\n        label: '免费',\n        color: 'bg-green-100 text-green-800'\n    },\n    FREEMIUM: {\n        value: 'freemium',\n        label: '免费增值',\n        color: 'bg-blue-100 text-blue-800'\n    },\n    PAID: {\n        value: 'paid',\n        label: '付费',\n        color: 'bg-orange-100 text-orange-800'\n    }\n};\n// 工具定价选项（用于筛选）\nconst TOOL_PRICING_OPTIONS = [\n    {\n        value: '',\n        label: '所有价格'\n    },\n    {\n        value: TOOL_PRICING_TYPES.FREE.value,\n        label: TOOL_PRICING_TYPES.FREE.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.FREEMIUM.value,\n        label: TOOL_PRICING_TYPES.FREEMIUM.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.PAID.value,\n        label: TOOL_PRICING_TYPES.PAID.label\n    }\n];\n// 工具定价选项（用于表单）\nconst TOOL_PRICING_FORM_OPTIONS = [\n    {\n        value: TOOL_PRICING_TYPES.FREE.value,\n        label: TOOL_PRICING_TYPES.FREE.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.FREEMIUM.value,\n        label: TOOL_PRICING_TYPES.FREEMIUM.label\n    },\n    {\n        value: TOOL_PRICING_TYPES.PAID.value,\n        label: TOOL_PRICING_TYPES.PAID.label\n    }\n];\n// 辅助函数\nconst getPricingConfig = (optionId)=>{\n    return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\nconst getToolPricingColor = (pricing)=>{\n    switch(pricing){\n        case TOOL_PRICING_TYPES.FREE.value:\n            return TOOL_PRICING_TYPES.FREE.color;\n        case TOOL_PRICING_TYPES.FREEMIUM.value:\n            return TOOL_PRICING_TYPES.FREEMIUM.color;\n        case TOOL_PRICING_TYPES.PAID.value:\n            return TOOL_PRICING_TYPES.PAID.color;\n        default:\n            return 'bg-gray-100 text-gray-800';\n    }\n};\nconst getToolPricingText = (pricing)=>{\n    switch(pricing){\n        case TOOL_PRICING_TYPES.FREE.value:\n            return TOOL_PRICING_TYPES.FREE.label;\n        case TOOL_PRICING_TYPES.FREEMIUM.value:\n            return TOOL_PRICING_TYPES.FREEMIUM.label;\n        case TOOL_PRICING_TYPES.PAID.value:\n            return TOOL_PRICING_TYPES.PAID.label;\n        default:\n            return pricing;\n    }\n};\n// 格式化价格显示\nconst formatPrice = (price)=>{\n    return price === 0 ? '免费' : `¥${price}`;\n};\n// 格式化Stripe金额显示\nconst formatStripeAmount = (amount, currency = 'cny')=>{\n    return new Intl.NumberFormat('zh-CN', {\n        style: 'currency',\n        currency: currency.toUpperCase(),\n        minimumFractionDigits: 2\n    }).format(amount / 100);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/constants/pricing.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function dbConnect() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/lib/seo/structuredData.ts":
/*!***************************************!*\
  !*** ./src/lib/seo/structuredData.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBreadcrumbStructuredData: () => (/* binding */ getBreadcrumbStructuredData),\n/* harmony export */   getOrganizationStructuredData: () => (/* binding */ getOrganizationStructuredData),\n/* harmony export */   getToolListStructuredData: () => (/* binding */ getToolListStructuredData),\n/* harmony export */   getToolStructuredData: () => (/* binding */ getToolStructuredData),\n/* harmony export */   getWebsiteStructuredData: () => (/* binding */ getWebsiteStructuredData)\n/* harmony export */ });\nconst baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';\n// 网站基础结构化数据\nfunction getWebsiteStructuredData() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"WebSite\",\n        \"name\": \"AI工具导航\",\n        \"description\": \"发现最好的AI工具，提升您的工作效率和创造力\",\n        \"url\": baseUrl,\n        \"potentialAction\": {\n            \"@type\": \"SearchAction\",\n            \"target\": {\n                \"@type\": \"EntryPoint\",\n                \"urlTemplate\": `${baseUrl}/tools?search={search_term_string}`\n            },\n            \"query-input\": \"required name=search_term_string\"\n        },\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"AI工具导航\",\n            \"url\": baseUrl\n        }\n    };\n}\n// 组织结构化数据\nfunction getOrganizationStructuredData() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Organization\",\n        \"name\": \"AI工具导航\",\n        \"description\": \"专业的AI工具发现和推荐平台\",\n        \"url\": baseUrl,\n        \"logo\": `${baseUrl}/logo.png`,\n        \"sameAs\": []\n    };\n}\n// 工具详情页结构化数据\nfunction getToolStructuredData(tool) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"SoftwareApplication\",\n        \"name\": tool.name,\n        \"description\": tool.description,\n        \"url\": tool.website,\n        \"applicationCategory\": \"AI工具\",\n        \"operatingSystem\": \"Web\",\n        \"offers\": {\n            \"@type\": \"Offer\",\n            \"price\": tool.pricing === 'free' ? \"0\" : undefined,\n            \"priceCurrency\": \"USD\",\n            \"availability\": \"https://schema.org/InStock\"\n        },\n        \"aggregateRating\": tool.likes ? {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": Math.min(5, Math.max(1, tool.likes / 10 + 3)),\n            \"reviewCount\": tool.likes,\n            \"bestRating\": 5,\n            \"worstRating\": 1\n        } : undefined,\n        \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`,\n        \"datePublished\": tool.publishedAt,\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"AI工具导航\",\n            \"url\": baseUrl\n        }\n    };\n}\n// 面包屑导航结构化数据\nfunction getBreadcrumbStructuredData(items) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"BreadcrumbList\",\n        \"itemListElement\": items.map((item, index)=>({\n                \"@type\": \"ListItem\",\n                \"position\": index + 1,\n                \"name\": item.name,\n                \"item\": `${baseUrl}${item.url}`\n            }))\n    };\n}\n// 工具列表页结构化数据\nfunction getToolListStructuredData(tools, category) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"ItemList\",\n        \"name\": category ? `${category} AI工具` : \"AI工具列表\",\n        \"description\": category ? `发现最好的${category} AI工具` : \"发现最好的AI工具\",\n        \"numberOfItems\": tools.length,\n        \"itemListElement\": tools.map((tool, index)=>({\n                \"@type\": \"ListItem\",\n                \"position\": index + 1,\n                \"item\": {\n                    \"@type\": \"SoftwareApplication\",\n                    \"name\": tool.name,\n                    \"description\": tool.description,\n                    \"url\": `${baseUrl}/tools/${tool._id}`,\n                    \"image\": tool.logo || `${baseUrl}/default-tool-image.jpg`\n                }\n            }))\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/lib/seo/structuredData.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./src/models/Tool.ts":
/*!****************************!*\
  !*** ./src/models/Tool.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ToolSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Tool name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Tool name cannot exceed 100 characters'\n        ]\n    },\n    tagline: {\n        type: String,\n        trim: true,\n        maxlength: [\n            200,\n            'Tagline cannot exceed 200 characters'\n        ]\n    },\n    description: {\n        type: String,\n        required: [\n            true,\n            'Tool description is required'\n        ],\n        trim: true,\n        maxlength: [\n            500,\n            'Description cannot exceed 500 characters'\n        ]\n    },\n    longDescription: {\n        type: String,\n        trim: true,\n        maxlength: [\n            2000,\n            'Long description cannot exceed 2000 characters'\n        ]\n    },\n    website: {\n        type: String,\n        required: [\n            true,\n            'Website URL is required'\n        ],\n        trim: true,\n        validate: {\n            validator: function(v) {\n                return /^https?:\\/\\/.+/.test(v);\n            },\n            message: 'Please enter a valid URL'\n        }\n    },\n    logo: {\n        type: String,\n        trim: true\n    },\n    category: {\n        type: String,\n        required: [\n            true,\n            'Category is required'\n        ],\n        enum: [\n            'text-generation',\n            'image-generation',\n            'video-generation',\n            'audio-generation',\n            'code-generation',\n            'data-analysis',\n            'productivity',\n            'design',\n            'marketing',\n            'education',\n            'research',\n            'other'\n        ]\n    },\n    tags: [\n        {\n            type: String,\n            trim: true,\n            lowercase: true\n        }\n    ],\n    pricing: {\n        type: String,\n        required: [\n            true,\n            'Pricing model is required'\n        ],\n        enum: [\n            'free',\n            'freemium',\n            'paid'\n        ]\n    },\n    pricingDetails: {\n        type: String,\n        trim: true,\n        maxlength: [\n            500,\n            'Pricing details cannot exceed 500 characters'\n        ]\n    },\n    screenshots: [\n        {\n            type: String,\n            trim: true\n        }\n    ],\n    submittedBy: {\n        type: String,\n        required: [\n            true,\n            'Submitter ID is required'\n        ],\n        trim: true\n    },\n    submittedAt: {\n        type: Date,\n        default: Date.now\n    },\n    publishedAt: {\n        type: Date\n    },\n    status: {\n        type: String,\n        required: true,\n        enum: [\n            'draft',\n            'pending',\n            'approved',\n            'published',\n            'rejected'\n        ],\n        default: 'draft'\n    },\n    reviewNotes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            1000,\n            'Review notes cannot exceed 1000 characters'\n        ]\n    },\n    reviewedBy: {\n        type: String,\n        trim: true\n    },\n    reviewedAt: {\n        type: Date\n    },\n    // 发布日期选择相关\n    launchDateSelected: {\n        type: Boolean,\n        default: false\n    },\n    selectedLaunchDate: {\n        type: Date\n    },\n    launchOption: {\n        type: String,\n        enum: [\n            'free',\n            'paid'\n        ]\n    },\n    // 付费相关\n    paymentRequired: {\n        type: Boolean,\n        default: false\n    },\n    paymentAmount: {\n        type: Number,\n        min: 0\n    },\n    paymentStatus: {\n        type: String,\n        enum: [\n            'pending',\n            'completed',\n            'failed',\n            'refunded'\n        ]\n    },\n    orderId: {\n        type: String,\n        trim: true\n    },\n    paymentMethod: {\n        type: String,\n        trim: true\n    },\n    paidAt: {\n        type: Date\n    },\n    views: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    likes: {\n        type: Number,\n        default: 0,\n        min: 0\n    },\n    likedBy: [\n        {\n            type: String,\n            trim: true\n        }\n    ],\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for better query performance\nToolSchema.index({\n    status: 1,\n    isActive: 1\n});\nToolSchema.index({\n    category: 1,\n    status: 1\n});\nToolSchema.index({\n    tags: 1,\n    status: 1\n});\nToolSchema.index({\n    submittedBy: 1\n});\nToolSchema.index({\n    publishedAt: -1\n});\nToolSchema.index({\n    views: -1\n});\nToolSchema.index({\n    likes: -1\n});\n// Text search index\nToolSchema.index({\n    name: 'text',\n    tagline: 'text',\n    description: 'text',\n    longDescription: 'text',\n    tags: 'text'\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Tool || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Tool', ToolSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/models/Tool.ts\n");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/react-icons"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();