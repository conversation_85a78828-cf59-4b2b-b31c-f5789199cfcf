/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/core/errors.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/core/errors.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(rsc)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(rsc)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(rsc)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(rsc)/./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(rsc)/./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"(rsc)/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"(rsc)/./node_modules/@babel/runtime/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(rsc)/./node_modules/@babel/runtime/helpers/inherits.js\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"(rsc)/./node_modules/@babel/runtime/helpers/wrapNativeSuper.js\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/index.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/core/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.AuthHandler = AuthHandler;\nvar _logger = _interopRequireWildcard(__webpack_require__(/*! ../utils/logger */ \"(rsc)/./node_modules/next-auth/utils/logger.js\"));\nvar _detectOrigin = __webpack_require__(/*! ../utils/detect-origin */ \"(rsc)/./node_modules/next-auth/utils/detect-origin.js\");\nvar routes = _interopRequireWildcard(__webpack_require__(/*! ./routes */ \"(rsc)/./node_modules/next-auth/core/routes/index.js\"));\nvar _pages = _interopRequireDefault(__webpack_require__(/*! ./pages */ \"(rsc)/./node_modules/next-auth/core/pages/index.js\"));\nvar _init = __webpack_require__(/*! ./init */ \"(rsc)/./node_modules/next-auth/core/init.js\");\nvar _assert = __webpack_require__(/*! ./lib/assert */ \"(rsc)/./node_modules/next-auth/core/lib/assert.js\");\nvar _cookie = __webpack_require__(/*! ./lib/cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\");\nvar _cookie2 = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/cookie/index.js\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function getBody(req) {\n  try {\n    return await req.json();\n  } catch (_unused) {}\n}\nasync function toInternalRequest(req) {\n  var _headers$xForwarded2;\n  if (req instanceof Request) {\n    var _req$headers$get, _url$searchParams$get, _headers$xForwarded;\n    const url = new URL(req.url);\n    const nextauth = url.pathname.split(\"/\").slice(3);\n    const headers = Object.fromEntries(req.headers);\n    const query = Object.fromEntries(url.searchParams);\n    query.nextauth = nextauth;\n    return {\n      action: nextauth[0],\n      method: req.method,\n      headers,\n      body: await getBody(req),\n      cookies: (0, _cookie2.parse)((_req$headers$get = req.headers.get(\"cookie\")) !== null && _req$headers$get !== void 0 ? _req$headers$get : \"\"),\n      providerId: nextauth[1],\n      error: (_url$searchParams$get = url.searchParams.get(\"error\")) !== null && _url$searchParams$get !== void 0 ? _url$searchParams$get : nextauth[1],\n      origin: (0, _detectOrigin.detectOrigin)((_headers$xForwarded = headers[\"x-forwarded-host\"]) !== null && _headers$xForwarded !== void 0 ? _headers$xForwarded : headers.host, headers[\"x-forwarded-proto\"]),\n      query\n    };\n  }\n  const {\n    headers\n  } = req;\n  const host = (_headers$xForwarded2 = headers === null || headers === void 0 ? void 0 : headers[\"x-forwarded-host\"]) !== null && _headers$xForwarded2 !== void 0 ? _headers$xForwarded2 : headers === null || headers === void 0 ? void 0 : headers.host;\n  req.origin = (0, _detectOrigin.detectOrigin)(host, headers === null || headers === void 0 ? void 0 : headers[\"x-forwarded-proto\"]);\n  return req;\n}\nasync function AuthHandler(params) {\n  var _req$body$callbackUrl, _req$body, _req$query2, _req$body2;\n  const {\n    options: authOptions,\n    req: incomingRequest\n  } = params;\n  const req = await toInternalRequest(incomingRequest);\n  (0, _logger.setLogger)(authOptions.logger, authOptions.debug);\n  const assertionResult = (0, _assert.assertConfig)({\n    options: authOptions,\n    req\n  });\n  if (Array.isArray(assertionResult)) {\n    assertionResult.forEach(_logger.default.warn);\n  } else if (assertionResult instanceof Error) {\n    var _req$query;\n    _logger.default.error(assertionResult.code, assertionResult);\n    const htmlPages = [\"signin\", \"signout\", \"error\", \"verify-request\"];\n    if (!htmlPages.includes(req.action) || req.method !== \"GET\") {\n      const message = `There is a problem with the server configuration. Check the server logs for more information.`;\n      return {\n        status: 500,\n        headers: [{\n          key: \"Content-Type\",\n          value: \"application/json\"\n        }],\n        body: {\n          message\n        }\n      };\n    }\n    const {\n      pages,\n      theme\n    } = authOptions;\n    const authOnErrorPage = (pages === null || pages === void 0 ? void 0 : pages.error) && ((_req$query = req.query) === null || _req$query === void 0 || (_req$query = _req$query.callbackUrl) === null || _req$query === void 0 ? void 0 : _req$query.startsWith(pages.error));\n    if (!(pages !== null && pages !== void 0 && pages.error) || authOnErrorPage) {\n      if (authOnErrorPage) {\n        _logger.default.error(\"AUTH_ON_ERROR_PAGE_ERROR\", new Error(`The error page ${pages === null || pages === void 0 ? void 0 : pages.error} should not require authentication`));\n      }\n      const render = (0, _pages.default)({\n        theme\n      });\n      return render.error({\n        error: \"configuration\"\n      });\n    }\n    return {\n      redirect: `${pages.error}?error=Configuration`\n    };\n  }\n  const {\n    action,\n    providerId,\n    error,\n    method = \"GET\"\n  } = req;\n  const {\n    options,\n    cookies\n  } = await (0, _init.init)({\n    authOptions,\n    action,\n    providerId,\n    origin: req.origin,\n    callbackUrl: (_req$body$callbackUrl = (_req$body = req.body) === null || _req$body === void 0 ? void 0 : _req$body.callbackUrl) !== null && _req$body$callbackUrl !== void 0 ? _req$body$callbackUrl : (_req$query2 = req.query) === null || _req$query2 === void 0 ? void 0 : _req$query2.callbackUrl,\n    csrfToken: (_req$body2 = req.body) === null || _req$body2 === void 0 ? void 0 : _req$body2.csrfToken,\n    cookies: req.cookies,\n    isPost: method === \"POST\"\n  });\n  const sessionStore = new _cookie.SessionStore(options.cookies.sessionToken, req, options.logger);\n  if (method === \"GET\") {\n    const render = (0, _pages.default)({\n      ...options,\n      query: req.query,\n      cookies\n    });\n    const {\n      pages\n    } = options;\n    switch (action) {\n      case \"providers\":\n        return await routes.providers(options.providers);\n      case \"session\":\n        {\n          const session = await routes.session({\n            options,\n            sessionStore\n          });\n          if (session.cookies) cookies.push(...session.cookies);\n          return {\n            ...session,\n            cookies\n          };\n        }\n      case \"csrf\":\n        return {\n          headers: [{\n            key: \"Content-Type\",\n            value: \"application/json\"\n          }],\n          body: {\n            csrfToken: options.csrfToken\n          },\n          cookies\n        };\n      case \"signin\":\n        if (pages.signIn) {\n          let signinUrl = `${pages.signIn}${pages.signIn.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(options.callbackUrl)}`;\n          if (error) signinUrl = `${signinUrl}&error=${encodeURIComponent(error)}`;\n          return {\n            redirect: signinUrl,\n            cookies\n          };\n        }\n        return render.signin();\n      case \"signout\":\n        if (pages.signOut) return {\n          redirect: pages.signOut,\n          cookies\n        };\n        return render.signout();\n      case \"callback\":\n        if (options.provider) {\n          const callback = await routes.callback({\n            body: req.body,\n            query: req.query,\n            headers: req.headers,\n            cookies: req.cookies,\n            method,\n            options,\n            sessionStore\n          });\n          if (callback.cookies) cookies.push(...callback.cookies);\n          return {\n            ...callback,\n            cookies\n          };\n        }\n        break;\n      case \"verify-request\":\n        if (pages.verifyRequest) {\n          return {\n            redirect: pages.verifyRequest,\n            cookies\n          };\n        }\n        return render.verifyRequest();\n      case \"error\":\n        if ([\"Signin\", \"OAuthSignin\", \"OAuthCallback\", \"OAuthCreateAccount\", \"EmailCreateAccount\", \"Callback\", \"OAuthAccountNotLinked\", \"EmailSignin\", \"CredentialsSignin\", \"SessionRequired\"].includes(error)) {\n          return {\n            redirect: `${options.url}/signin?error=${error}`,\n            cookies\n          };\n        }\n        if (pages.error) {\n          return {\n            redirect: `${pages.error}${pages.error.includes(\"?\") ? \"&\" : \"?\"}error=${error}`,\n            cookies\n          };\n        }\n        return render.error({\n          error: error\n        });\n      default:\n    }\n  } else if (method === \"POST\") {\n    switch (action) {\n      case \"signin\":\n        if (options.csrfTokenVerified && options.provider) {\n          const signin = await routes.signin({\n            query: req.query,\n            body: req.body,\n            options\n          });\n          if (signin.cookies) cookies.push(...signin.cookies);\n          return {\n            ...signin,\n            cookies\n          };\n        }\n        return {\n          redirect: `${options.url}/signin?csrf=true`,\n          cookies\n        };\n      case \"signout\":\n        if (options.csrfTokenVerified) {\n          const signout = await routes.signout({\n            options,\n            sessionStore\n          });\n          if (signout.cookies) cookies.push(...signout.cookies);\n          return {\n            ...signout,\n            cookies\n          };\n        }\n        return {\n          redirect: `${options.url}/signout?csrf=true`,\n          cookies\n        };\n      case \"callback\":\n        if (options.provider) {\n          if (options.provider.type === \"credentials\" && !options.csrfTokenVerified) {\n            return {\n              redirect: `${options.url}/signin?csrf=true`,\n              cookies\n            };\n          }\n          const callback = await routes.callback({\n            body: req.body,\n            query: req.query,\n            headers: req.headers,\n            cookies: req.cookies,\n            method,\n            options,\n            sessionStore\n          });\n          if (callback.cookies) cookies.push(...callback.cookies);\n          return {\n            ...callback,\n            cookies\n          };\n        }\n        break;\n      case \"_log\":\n        {\n          if (authOptions.logger) {\n            try {\n              var _req$body3;\n              const {\n                code,\n                level,\n                ...metadata\n              } = (_req$body3 = req.body) !== null && _req$body3 !== void 0 ? _req$body3 : {};\n              _logger.default[level](code, metadata);\n            } catch (error) {\n              _logger.default.error(\"LOGGER_ERROR\", error);\n            }\n          }\n          return {};\n        }\n      case \"session\":\n        {\n          if (options.csrfTokenVerified) {\n            var _req$body4;\n            const session = await routes.session({\n              options,\n              sessionStore,\n              newSession: (_req$body4 = req.body) === null || _req$body4 === void 0 ? void 0 : _req$body4.data,\n              isUpdate: true\n            });\n            if (session.cookies) cookies.push(...session.cookies);\n            return {\n              ...session,\n              cookies\n            };\n          }\n          return {\n            status: 400,\n            body: {},\n            cookies\n          };\n        }\n      default:\n    }\n  }\n  return {\n    status: 400,\n    body: `Error: This action with HTTP ${method} is not supported by NextAuth.js`\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/init.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/core/init.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.init = init;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar _logger = _interopRequireDefault(__webpack_require__(/*! ../utils/logger */ \"(rsc)/./node_modules/next-auth/utils/logger.js\"));\nvar _errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nvar _providers = _interopRequireDefault(__webpack_require__(/*! ./lib/providers */ \"(rsc)/./node_modules/next-auth/core/lib/providers.js\"));\nvar _utils = __webpack_require__(/*! ./lib/utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nvar cookie = _interopRequireWildcard(__webpack_require__(/*! ./lib/cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\"));\nvar jwt = _interopRequireWildcard(__webpack_require__(/*! ../jwt */ \"(rsc)/./node_modules/next-auth/jwt/index.js\"));\nvar _defaultCallbacks = __webpack_require__(/*! ./lib/default-callbacks */ \"(rsc)/./node_modules/next-auth/core/lib/default-callbacks.js\");\nvar _csrfToken = __webpack_require__(/*! ./lib/csrf-token */ \"(rsc)/./node_modules/next-auth/core/lib/csrf-token.js\");\nvar _callbackUrl = __webpack_require__(/*! ./lib/callback-url */ \"(rsc)/./node_modules/next-auth/core/lib/callback-url.js\");\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../utils/parse-url */ \"(rsc)/./node_modules/next-auth/utils/parse-url.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function init({\n  authOptions,\n  providerId,\n  action,\n  origin,\n  cookies: reqCookies,\n  callbackUrl: reqCallbackUrl,\n  csrfToken: reqCsrfToken,\n  isPost\n}) {\n  var _authOptions$useSecur, _authOptions$events;\n  const url = (0, _parseUrl.default)(origin);\n  const secret = (0, _utils.createSecret)({\n    authOptions,\n    url\n  });\n  const {\n    providers,\n    provider\n  } = (0, _providers.default)({\n    providers: authOptions.providers,\n    url,\n    providerId\n  });\n  const maxAge = 30 * 24 * 60 * 60;\n  const options = {\n    debug: false,\n    pages: {},\n    theme: {\n      colorScheme: \"auto\",\n      logo: \"\",\n      brandColor: \"\",\n      buttonText: \"\"\n    },\n    ...authOptions,\n    url,\n    action,\n    provider,\n    cookies: {\n      ...cookie.defaultCookies((_authOptions$useSecur = authOptions.useSecureCookies) !== null && _authOptions$useSecur !== void 0 ? _authOptions$useSecur : url.base.startsWith(\"https://\")),\n      ...authOptions.cookies\n    },\n    secret,\n    providers,\n    session: {\n      strategy: authOptions.adapter ? \"database\" : \"jwt\",\n      maxAge,\n      updateAge: 24 * 60 * 60,\n      generateSessionToken: () => {\n        var _randomUUID;\n        return (_randomUUID = _crypto.randomUUID === null || _crypto.randomUUID === void 0 ? void 0 : (0, _crypto.randomUUID)()) !== null && _randomUUID !== void 0 ? _randomUUID : (0, _crypto.randomBytes)(32).toString(\"hex\");\n      },\n      ...authOptions.session\n    },\n    jwt: {\n      secret,\n      maxAge,\n      encode: jwt.encode,\n      decode: jwt.decode,\n      ...authOptions.jwt\n    },\n    events: (0, _errors.eventsErrorHandler)((_authOptions$events = authOptions.events) !== null && _authOptions$events !== void 0 ? _authOptions$events : {}, _logger.default),\n    adapter: (0, _errors.adapterErrorHandler)(authOptions.adapter, _logger.default),\n    callbacks: {\n      ..._defaultCallbacks.defaultCallbacks,\n      ...authOptions.callbacks\n    },\n    logger: _logger.default,\n    callbackUrl: url.origin\n  };\n  const cookies = [];\n  const {\n    csrfToken,\n    cookie: csrfCookie,\n    csrfTokenVerified\n  } = (0, _csrfToken.createCSRFToken)({\n    options,\n    cookieValue: reqCookies === null || reqCookies === void 0 ? void 0 : reqCookies[options.cookies.csrfToken.name],\n    isPost,\n    bodyValue: reqCsrfToken\n  });\n  options.csrfToken = csrfToken;\n  options.csrfTokenVerified = csrfTokenVerified;\n  if (csrfCookie) {\n    cookies.push({\n      name: options.cookies.csrfToken.name,\n      value: csrfCookie,\n      options: options.cookies.csrfToken.options\n    });\n  }\n  const {\n    callbackUrl,\n    callbackUrlCookie\n  } = await (0, _callbackUrl.createCallbackUrl)({\n    options,\n    cookieValue: reqCookies === null || reqCookies === void 0 ? void 0 : reqCookies[options.cookies.callbackUrl.name],\n    paramValue: reqCallbackUrl\n  });\n  options.callbackUrl = callbackUrl;\n  if (callbackUrlCookie) {\n    cookies.push({\n      name: options.cookies.callbackUrl.name,\n      value: callbackUrlCookie,\n      options: options.cookies.callbackUrl.options\n    });\n  }\n  return {\n    options,\n    cookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/init.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/assert.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/core/lib/assert.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.assertConfig = assertConfig;\nvar _errors = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../../utils/parse-url */ \"(rsc)/./node_modules/next-auth/utils/parse-url.js\"));\nvar _cookie = __webpack_require__(/*! ./cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\");\nlet warned = false;\nfunction isValidHttpUrl(url, baseUrl) {\n  try {\n    return /^https?:/.test(new URL(url, url.startsWith(\"/\") ? baseUrl : undefined).protocol);\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction assertConfig(params) {\n  var _req$query, _req$query2, _options$useSecureCoo, _req$cookies, _options$cookies$call, _options$cookies;\n  const {\n    options,\n    req\n  } = params;\n  const warnings = [];\n  if (!warned) {\n    if (!req.origin) warnings.push(\"NEXTAUTH_URL\");\n    if (!options.secret && \"development\" !== \"production\") warnings.push(\"NO_SECRET\");\n    if (options.debug) warnings.push(\"DEBUG_ENABLED\");\n  }\n  if (!options.secret && \"development\" === \"production\") {}\n  if (!((_req$query = req.query) !== null && _req$query !== void 0 && _req$query.nextauth) && !req.action) {\n    return new _errors.MissingAPIRoute(\"Cannot find [...nextauth].{js,ts} in `/pages/api/auth`. Make sure the filename is written correctly.\");\n  }\n  const callbackUrlParam = (_req$query2 = req.query) === null || _req$query2 === void 0 ? void 0 : _req$query2.callbackUrl;\n  const url = (0, _parseUrl.default)(req.origin);\n  if (callbackUrlParam && !isValidHttpUrl(callbackUrlParam, url.base)) {\n    return new _errors.InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlParam}`);\n  }\n  const {\n    callbackUrl: defaultCallbackUrl\n  } = (0, _cookie.defaultCookies)((_options$useSecureCoo = options.useSecureCookies) !== null && _options$useSecureCoo !== void 0 ? _options$useSecureCoo : url.base.startsWith(\"https://\"));\n  const callbackUrlCookie = (_req$cookies = req.cookies) === null || _req$cookies === void 0 ? void 0 : _req$cookies[(_options$cookies$call = (_options$cookies = options.cookies) === null || _options$cookies === void 0 || (_options$cookies = _options$cookies.callbackUrl) === null || _options$cookies === void 0 ? void 0 : _options$cookies.name) !== null && _options$cookies$call !== void 0 ? _options$cookies$call : defaultCallbackUrl.name];\n  if (callbackUrlCookie && !isValidHttpUrl(callbackUrlCookie, url.base)) {\n    return new _errors.InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlCookie}`);\n  }\n  let hasCredentials, hasEmail;\n  let hasTwitterOAuth2;\n  for (const provider of options.providers) {\n    if (provider.type === \"credentials\") hasCredentials = true;else if (provider.type === \"email\") hasEmail = true;else if (provider.id === \"twitter\" && provider.version === \"2.0\") hasTwitterOAuth2 = true;\n  }\n  if (hasCredentials) {\n    var _options$session;\n    const dbStrategy = ((_options$session = options.session) === null || _options$session === void 0 ? void 0 : _options$session.strategy) === \"database\";\n    const onlyCredentials = !options.providers.some(p => p.type !== \"credentials\");\n    if (dbStrategy && onlyCredentials) {\n      return new _errors.UnsupportedStrategy(\"Signin in with credentials only supported if JWT strategy is enabled\");\n    }\n    const credentialsNoAuthorize = options.providers.some(p => p.type === \"credentials\" && !p.authorize);\n    if (credentialsNoAuthorize) {\n      return new _errors.MissingAuthorize(\"Must define an authorize() handler to use credentials authentication provider\");\n    }\n  }\n  if (hasEmail) {\n    const {\n      adapter\n    } = options;\n    if (!adapter) {\n      return new _errors.MissingAdapter(\"E-mail login requires an adapter.\");\n    }\n    const missingMethods = [\"createVerificationToken\", \"useVerificationToken\", \"getUserByEmail\"].filter(method => !adapter[method]);\n    if (missingMethods.length) {\n      return new _errors.MissingAdapterMethods(`Required adapter methods were missing: ${missingMethods.join(\", \")}`);\n    }\n  }\n  if (!warned) {\n    if (hasTwitterOAuth2) warnings.push(\"TWITTER_OAUTH_2_BETA\");\n    warned = true;\n  }\n  return warnings;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/callback-handler.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/callback-handler.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = callbackHandler;\nvar _errors = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nvar _utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nasync function callbackHandler(params) {\n  const {\n    sessionToken,\n    profile: _profile,\n    account,\n    options\n  } = params;\n  if (!(account !== null && account !== void 0 && account.providerAccountId) || !account.type) throw new Error(\"Missing or invalid provider account\");\n  if (![\"email\", \"oauth\"].includes(account.type)) throw new Error(\"Provider not supported\");\n  const {\n    adapter,\n    jwt,\n    events,\n    session: {\n      strategy: sessionStrategy,\n      generateSessionToken\n    }\n  } = options;\n  if (!adapter) {\n    return {\n      user: _profile,\n      account\n    };\n  }\n  const profile = _profile;\n  const {\n    createUser,\n    updateUser,\n    getUser,\n    getUserByAccount,\n    getUserByEmail,\n    linkAccount,\n    createSession,\n    getSessionAndUser,\n    deleteSession\n  } = adapter;\n  let session = null;\n  let user = null;\n  let isNewUser = false;\n  const useJwtSession = sessionStrategy === \"jwt\";\n  if (sessionToken) {\n    if (useJwtSession) {\n      try {\n        session = await jwt.decode({\n          ...jwt,\n          token: sessionToken\n        });\n        if (session && \"sub\" in session && session.sub) {\n          user = await getUser(session.sub);\n        }\n      } catch (_unused) {}\n    } else {\n      const userAndSession = await getSessionAndUser(sessionToken);\n      if (userAndSession) {\n        session = userAndSession.session;\n        user = userAndSession.user;\n      }\n    }\n  }\n  if (account.type === \"email\") {\n    const userByEmail = await getUserByEmail(profile.email);\n    if (userByEmail) {\n      var _user, _events$updateUser;\n      if (((_user = user) === null || _user === void 0 ? void 0 : _user.id) !== userByEmail.id && !useJwtSession && sessionToken) {\n        await deleteSession(sessionToken);\n      }\n      user = await updateUser({\n        id: userByEmail.id,\n        emailVerified: new Date()\n      });\n      await ((_events$updateUser = events.updateUser) === null || _events$updateUser === void 0 ? void 0 : _events$updateUser.call(events, {\n        user\n      }));\n    } else {\n      var _events$createUser;\n      const {\n        id: _,\n        ...newUser\n      } = {\n        ...profile,\n        emailVerified: new Date()\n      };\n      user = await createUser(newUser);\n      await ((_events$createUser = events.createUser) === null || _events$createUser === void 0 ? void 0 : _events$createUser.call(events, {\n        user\n      }));\n      isNewUser = true;\n    }\n    session = useJwtSession ? {} : await createSession({\n      sessionToken: await generateSessionToken(),\n      userId: user.id,\n      expires: (0, _utils.fromDate)(options.session.maxAge)\n    });\n    return {\n      session,\n      user,\n      isNewUser\n    };\n  } else if (account.type === \"oauth\") {\n    const userByAccount = await getUserByAccount({\n      providerAccountId: account.providerAccountId,\n      provider: account.provider\n    });\n    if (userByAccount) {\n      if (user) {\n        if (userByAccount.id === user.id) {\n          return {\n            session,\n            user,\n            isNewUser\n          };\n        }\n        throw new _errors.AccountNotLinkedError(\"The account is already associated with another user\");\n      }\n      session = useJwtSession ? {} : await createSession({\n        sessionToken: await generateSessionToken(),\n        userId: userByAccount.id,\n        expires: (0, _utils.fromDate)(options.session.maxAge)\n      });\n      return {\n        session,\n        user: userByAccount,\n        isNewUser\n      };\n    } else {\n      var _events$createUser2, _events$linkAccount2;\n      if (user) {\n        var _events$linkAccount;\n        await linkAccount({\n          ...account,\n          userId: user.id\n        });\n        await ((_events$linkAccount = events.linkAccount) === null || _events$linkAccount === void 0 ? void 0 : _events$linkAccount.call(events, {\n          user,\n          account,\n          profile\n        }));\n        return {\n          session,\n          user,\n          isNewUser\n        };\n      }\n      const userByEmail = profile.email ? await getUserByEmail(profile.email) : null;\n      if (userByEmail) {\n        const provider = options.provider;\n        if (provider !== null && provider !== void 0 && provider.allowDangerousEmailAccountLinking) {\n          user = userByEmail;\n        } else {\n          throw new _errors.AccountNotLinkedError(\"Another account already exists with the same e-mail address\");\n        }\n      } else {\n        const {\n          id: _,\n          ...newUser\n        } = {\n          ...profile,\n          emailVerified: null\n        };\n        user = await createUser(newUser);\n      }\n      await ((_events$createUser2 = events.createUser) === null || _events$createUser2 === void 0 ? void 0 : _events$createUser2.call(events, {\n        user\n      }));\n      await linkAccount({\n        ...account,\n        userId: user.id\n      });\n      await ((_events$linkAccount2 = events.linkAccount) === null || _events$linkAccount2 === void 0 ? void 0 : _events$linkAccount2.call(events, {\n        user,\n        account,\n        profile\n      }));\n      session = useJwtSession ? {} : await createSession({\n        sessionToken: await generateSessionToken(),\n        userId: user.id,\n        expires: (0, _utils.fromDate)(options.session.maxAge)\n      });\n      return {\n        session,\n        user,\n        isNewUser: true\n      };\n    }\n  }\n  throw new Error(\"Unsupported account type\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/callback-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/callback-url.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/callback-url.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createCallbackUrl = createCallbackUrl;\nasync function createCallbackUrl({\n  options,\n  paramValue,\n  cookieValue\n}) {\n  const {\n    url,\n    callbacks\n  } = options;\n  let callbackUrl = url.origin;\n  if (paramValue) {\n    callbackUrl = await callbacks.redirect({\n      url: paramValue,\n      baseUrl: url.origin\n    });\n  } else if (cookieValue) {\n    callbackUrl = await callbacks.redirect({\n      url: cookieValue,\n      baseUrl: url.origin\n    });\n  }\n  return {\n    callbackUrl,\n    callbackUrlCookie: callbackUrl !== cookieValue ? callbackUrl : undefined\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2NhbGxiYWNrLXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2NhbGxiYWNrLXVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuY3JlYXRlQ2FsbGJhY2tVcmwgPSBjcmVhdGVDYWxsYmFja1VybDtcbmFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNhbGxiYWNrVXJsKHtcbiAgb3B0aW9ucyxcbiAgcGFyYW1WYWx1ZSxcbiAgY29va2llVmFsdWVcbn0pIHtcbiAgY29uc3Qge1xuICAgIHVybCxcbiAgICBjYWxsYmFja3NcbiAgfSA9IG9wdGlvbnM7XG4gIGxldCBjYWxsYmFja1VybCA9IHVybC5vcmlnaW47XG4gIGlmIChwYXJhbVZhbHVlKSB7XG4gICAgY2FsbGJhY2tVcmwgPSBhd2FpdCBjYWxsYmFja3MucmVkaXJlY3Qoe1xuICAgICAgdXJsOiBwYXJhbVZhbHVlLFxuICAgICAgYmFzZVVybDogdXJsLm9yaWdpblxuICAgIH0pO1xuICB9IGVsc2UgaWYgKGNvb2tpZVZhbHVlKSB7XG4gICAgY2FsbGJhY2tVcmwgPSBhd2FpdCBjYWxsYmFja3MucmVkaXJlY3Qoe1xuICAgICAgdXJsOiBjb29raWVWYWx1ZSxcbiAgICAgIGJhc2VVcmw6IHVybC5vcmlnaW5cbiAgICB9KTtcbiAgfVxuICByZXR1cm4ge1xuICAgIGNhbGxiYWNrVXJsLFxuICAgIGNhbGxiYWNrVXJsQ29va2llOiBjYWxsYmFja1VybCAhPT0gY29va2llVmFsdWUgPyBjYWxsYmFja1VybCA6IHVuZGVmaW5lZFxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/callback-url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/cookie.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/core/lib/cookie.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.SessionStore = void 0;\nexports.defaultCookies = defaultCookies;\nfunction _classPrivateMethodInitSpec(e, a) { _checkPrivateRedeclaration(e, a), a.add(e); }\nfunction _classPrivateFieldInitSpec(e, t, a) { _checkPrivateRedeclaration(e, t), t.set(e, a); }\nfunction _checkPrivateRedeclaration(e, t) { if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\"); }\nfunction _classPrivateFieldGet(s, a) { return s.get(_assertClassBrand(s, a)); }\nfunction _classPrivateFieldSet(s, a, r) { return s.set(_assertClassBrand(s, a), r), r; }\nfunction _assertClassBrand(e, t, n) { if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n; throw new TypeError(\"Private element is not present on this object\"); }\nconst ALLOWED_COOKIE_SIZE = 4096;\nconst ESTIMATED_EMPTY_COOKIE_SIZE = 163;\nconst CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;\nfunction defaultCookies(useSecureCookies) {\n  const cookiePrefix = useSecureCookies ? \"__Secure-\" : \"\";\n  return {\n    sessionToken: {\n      name: `${cookiePrefix}next-auth.session-token`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    callbackUrl: {\n      name: `${cookiePrefix}next-auth.callback-url`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    csrfToken: {\n      name: `${useSecureCookies ? \"__Host-\" : \"\"}next-auth.csrf-token`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    pkceCodeVerifier: {\n      name: `${cookiePrefix}next-auth.pkce.code_verifier`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies,\n        maxAge: 60 * 15\n      }\n    },\n    state: {\n      name: `${cookiePrefix}next-auth.state`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies,\n        maxAge: 60 * 15\n      }\n    },\n    nonce: {\n      name: `${cookiePrefix}next-auth.nonce`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    }\n  };\n}\nvar _chunks = new WeakMap();\nvar _option = new WeakMap();\nvar _logger = new WeakMap();\nvar _SessionStore_brand = new WeakSet();\nclass SessionStore {\n  constructor(option, req, logger) {\n    _classPrivateMethodInitSpec(this, _SessionStore_brand);\n    _classPrivateFieldInitSpec(this, _chunks, {});\n    _classPrivateFieldInitSpec(this, _option, void 0);\n    _classPrivateFieldInitSpec(this, _logger, void 0);\n    _classPrivateFieldSet(_logger, this, logger);\n    _classPrivateFieldSet(_option, this, option);\n    const {\n      cookies: _cookies\n    } = req;\n    const {\n      name: cookieName\n    } = option;\n    if (typeof (_cookies === null || _cookies === void 0 ? void 0 : _cookies.getAll) === \"function\") {\n      for (const {\n        name,\n        value\n      } of _cookies.getAll()) {\n        if (name.startsWith(cookieName)) {\n          _classPrivateFieldGet(_chunks, this)[name] = value;\n        }\n      }\n    } else if (_cookies instanceof Map) {\n      for (const name of _cookies.keys()) {\n        if (name.startsWith(cookieName)) _classPrivateFieldGet(_chunks, this)[name] = _cookies.get(name);\n      }\n    } else {\n      for (const name in _cookies) {\n        if (name.startsWith(cookieName)) _classPrivateFieldGet(_chunks, this)[name] = _cookies[name];\n      }\n    }\n  }\n  get value() {\n    const sortedKeys = Object.keys(_classPrivateFieldGet(_chunks, this)).sort((a, b) => {\n      var _a$split$pop, _b$split$pop;\n      const aSuffix = parseInt((_a$split$pop = a.split(\".\").pop()) !== null && _a$split$pop !== void 0 ? _a$split$pop : \"0\");\n      const bSuffix = parseInt((_b$split$pop = b.split(\".\").pop()) !== null && _b$split$pop !== void 0 ? _b$split$pop : \"0\");\n      return aSuffix - bSuffix;\n    });\n    return sortedKeys.map(key => _classPrivateFieldGet(_chunks, this)[key]).join(\"\");\n  }\n  chunk(value, options) {\n    const cookies = _assertClassBrand(_SessionStore_brand, this, _clean).call(this);\n    const chunked = _assertClassBrand(_SessionStore_brand, this, _chunk).call(this, {\n      name: _classPrivateFieldGet(_option, this).name,\n      value,\n      options: {\n        ..._classPrivateFieldGet(_option, this).options,\n        ...options\n      }\n    });\n    for (const chunk of chunked) {\n      cookies[chunk.name] = chunk;\n    }\n    return Object.values(cookies);\n  }\n  clean() {\n    return Object.values(_assertClassBrand(_SessionStore_brand, this, _clean).call(this));\n  }\n}\nexports.SessionStore = SessionStore;\nfunction _chunk(cookie) {\n  const chunkCount = Math.ceil(cookie.value.length / CHUNK_SIZE);\n  if (chunkCount === 1) {\n    _classPrivateFieldGet(_chunks, this)[cookie.name] = cookie.value;\n    return [cookie];\n  }\n  const cookies = [];\n  for (let i = 0; i < chunkCount; i++) {\n    const name = `${cookie.name}.${i}`;\n    const value = cookie.value.substr(i * CHUNK_SIZE, CHUNK_SIZE);\n    cookies.push({\n      ...cookie,\n      name,\n      value\n    });\n    _classPrivateFieldGet(_chunks, this)[name] = value;\n  }\n  _classPrivateFieldGet(_logger, this).debug(\"CHUNKING_SESSION_COOKIE\", {\n    message: `Session cookie exceeds allowed ${ALLOWED_COOKIE_SIZE} bytes.`,\n    emptyCookieSize: ESTIMATED_EMPTY_COOKIE_SIZE,\n    valueSize: cookie.value.length,\n    chunks: cookies.map(c => c.value.length + ESTIMATED_EMPTY_COOKIE_SIZE)\n  });\n  return cookies;\n}\nfunction _clean() {\n  const cleanedChunks = {};\n  for (const name in _classPrivateFieldGet(_chunks, this)) {\n    var _classPrivateFieldGet2;\n    (_classPrivateFieldGet2 = _classPrivateFieldGet(_chunks, this)) === null || _classPrivateFieldGet2 === void 0 || delete _classPrivateFieldGet2[name];\n    cleanedChunks[name] = {\n      name,\n      value: \"\",\n      options: {\n        ..._classPrivateFieldGet(_option, this).options,\n        maxAge: 0\n      }\n    };\n  }\n  return cleanedChunks;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/cookie.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/csrf-token.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/core/lib/csrf-token.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createCSRFToken = createCSRFToken;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nfunction createCSRFToken({\n  options,\n  cookieValue,\n  isPost,\n  bodyValue\n}) {\n  if (cookieValue) {\n    const [csrfToken, csrfTokenHash] = cookieValue.split(\"|\");\n    const expectedCsrfTokenHash = (0, _crypto.createHash)(\"sha256\").update(`${csrfToken}${options.secret}`).digest(\"hex\");\n    if (csrfTokenHash === expectedCsrfTokenHash) {\n      const csrfTokenVerified = isPost && csrfToken === bodyValue;\n      return {\n        csrfTokenVerified,\n        csrfToken\n      };\n    }\n  }\n  const csrfToken = (0, _crypto.randomBytes)(32).toString(\"hex\");\n  const csrfTokenHash = (0, _crypto.createHash)(\"sha256\").update(`${csrfToken}${options.secret}`).digest(\"hex\");\n  const cookie = `${csrfToken}|${csrfTokenHash}`;\n  return {\n    cookie,\n    csrfToken\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/csrf-token.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/default-callbacks.js":
/*!**************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/default-callbacks.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.defaultCallbacks = void 0;\nconst defaultCallbacks = exports.defaultCallbacks = {\n  signIn() {\n    return true;\n  },\n  redirect({\n    url,\n    baseUrl\n  }) {\n    if (url.startsWith(\"/\")) return `${baseUrl}${url}`;else if (new URL(url).origin === baseUrl) return url;\n    return baseUrl;\n  },\n  session({\n    session\n  }) {\n    return session;\n  },\n  jwt({\n    token\n  }) {\n    return token;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2RlZmF1bHQtY2FsbGJhY2tzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHdCQUF3QjtBQUN4Qix5QkFBeUIsd0JBQXdCO0FBQ2pEO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHVDQUF1QyxRQUFRLEVBQUUsSUFBSSxFQUFFO0FBQ3ZEO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2RlZmF1bHQtY2FsbGJhY2tzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0Q2FsbGJhY2tzID0gdm9pZCAwO1xuY29uc3QgZGVmYXVsdENhbGxiYWNrcyA9IGV4cG9ydHMuZGVmYXVsdENhbGxiYWNrcyA9IHtcbiAgc2lnbkluKCkge1xuICAgIHJldHVybiB0cnVlO1xuICB9LFxuICByZWRpcmVjdCh7XG4gICAgdXJsLFxuICAgIGJhc2VVcmxcbiAgfSkge1xuICAgIGlmICh1cmwuc3RhcnRzV2l0aChcIi9cIikpIHJldHVybiBgJHtiYXNlVXJsfSR7dXJsfWA7ZWxzZSBpZiAobmV3IFVSTCh1cmwpLm9yaWdpbiA9PT0gYmFzZVVybCkgcmV0dXJuIHVybDtcbiAgICByZXR1cm4gYmFzZVVybDtcbiAgfSxcbiAgc2Vzc2lvbih7XG4gICAgc2Vzc2lvblxuICB9KSB7XG4gICAgcmV0dXJuIHNlc3Npb247XG4gIH0sXG4gIGp3dCh7XG4gICAgdG9rZW5cbiAgfSkge1xuICAgIHJldHVybiB0b2tlbjtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/default-callbacks.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/email/getUserFromEmail.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = getAdapterUserFromEmail;\nasync function getAdapterUserFromEmail({\n  email,\n  adapter\n}) {\n  const {\n    getUserByEmail\n  } = adapter;\n  const adapterUser = email ? await getUserByEmail(email) : null;\n  if (adapterUser) return adapterUser;\n  return {\n    id: email,\n    email,\n    emailVerified: null\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2VtYWlsL2dldFVzZXJGcm9tRW1haWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtYXV0aC9jb3JlL2xpYi9lbWFpbC9nZXRVc2VyRnJvbUVtYWlsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gZ2V0QWRhcHRlclVzZXJGcm9tRW1haWw7XG5hc3luYyBmdW5jdGlvbiBnZXRBZGFwdGVyVXNlckZyb21FbWFpbCh7XG4gIGVtYWlsLFxuICBhZGFwdGVyXG59KSB7XG4gIGNvbnN0IHtcbiAgICBnZXRVc2VyQnlFbWFpbFxuICB9ID0gYWRhcHRlcjtcbiAgY29uc3QgYWRhcHRlclVzZXIgPSBlbWFpbCA/IGF3YWl0IGdldFVzZXJCeUVtYWlsKGVtYWlsKSA6IG51bGw7XG4gIGlmIChhZGFwdGVyVXNlcikgcmV0dXJuIGFkYXB0ZXJVc2VyO1xuICByZXR1cm4ge1xuICAgIGlkOiBlbWFpbCxcbiAgICBlbWFpbCxcbiAgICBlbWFpbFZlcmlmaWVkOiBudWxsXG4gIH07XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/email/signin.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/email/signin.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = email;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar _utils = __webpack_require__(/*! ../utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nasync function email(identifier, options) {\n  var _await$provider$gener, _provider$generateVer, _provider$maxAge, _adapter$createVerifi;\n  const {\n    url,\n    adapter,\n    provider,\n    callbackUrl,\n    theme\n  } = options;\n  const token = (_await$provider$gener = await ((_provider$generateVer = provider.generateVerificationToken) === null || _provider$generateVer === void 0 ? void 0 : _provider$generateVer.call(provider))) !== null && _await$provider$gener !== void 0 ? _await$provider$gener : (0, _crypto.randomBytes)(32).toString(\"hex\");\n  const ONE_DAY_IN_SECONDS = 86400;\n  const expires = new Date(Date.now() + ((_provider$maxAge = provider.maxAge) !== null && _provider$maxAge !== void 0 ? _provider$maxAge : ONE_DAY_IN_SECONDS) * 1000);\n  const params = new URLSearchParams({\n    callbackUrl,\n    token,\n    email: identifier\n  });\n  const _url = `${url}/callback/${provider.id}?${params}`;\n  await Promise.all([provider.sendVerificationRequest({\n    identifier,\n    token,\n    expires,\n    url: _url,\n    provider,\n    theme\n  }), (_adapter$createVerifi = adapter.createVerificationToken) === null || _adapter$createVerifi === void 0 ? void 0 : _adapter$createVerifi.call(adapter, {\n    identifier,\n    token: (0, _utils.hashToken)(token, options),\n    expires\n  })]);\n  return `${url}/verify-request?${new URLSearchParams({\n    provider: provider.id,\n    type: provider.type\n  })}`;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/email/signin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/authorization-url.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/authorization-url.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = getAuthorizationUrl;\nvar _client = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client.js\");\nvar _clientLegacy = __webpack_require__(/*! ./client-legacy */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js\");\nvar checks = _interopRequireWildcard(__webpack_require__(/*! ./checks */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function getAuthorizationUrl({\n  options,\n  query\n}) {\n  var _provider$version;\n  const {\n    logger,\n    provider\n  } = options;\n  let params = {};\n  if (typeof provider.authorization === \"string\") {\n    const parsedUrl = new URL(provider.authorization);\n    const parsedParams = Object.fromEntries(parsedUrl.searchParams);\n    params = {\n      ...params,\n      ...parsedParams\n    };\n  } else {\n    var _provider$authorizati;\n    params = {\n      ...params,\n      ...((_provider$authorizati = provider.authorization) === null || _provider$authorizati === void 0 ? void 0 : _provider$authorizati.params)\n    };\n  }\n  params = {\n    ...params,\n    ...query\n  };\n  if ((_provider$version = provider.version) !== null && _provider$version !== void 0 && _provider$version.startsWith(\"1.\")) {\n    var _provider$authorizati2;\n    const client = (0, _clientLegacy.oAuth1Client)(options);\n    const tokens = await client.getOAuthRequestToken(params);\n    const url = `${(_provider$authorizati2 = provider.authorization) === null || _provider$authorizati2 === void 0 ? void 0 : _provider$authorizati2.url}?${new URLSearchParams({\n      oauth_token: tokens.oauth_token,\n      oauth_token_secret: tokens.oauth_token_secret,\n      ...tokens.params\n    })}`;\n    _clientLegacy.oAuth1TokenStore.set(tokens.oauth_token, tokens.oauth_token_secret);\n    logger.debug(\"GET_AUTHORIZATION_URL\", {\n      url,\n      provider\n    });\n    return {\n      redirect: url\n    };\n  }\n  const client = await (0, _client.openidClient)(options);\n  const authorizationParams = params;\n  const cookies = [];\n  await checks.state.create(options, cookies, authorizationParams);\n  await checks.pkce.create(options, cookies, authorizationParams);\n  await checks.nonce.create(options, cookies, authorizationParams);\n  const url = client.authorizationUrl(authorizationParams);\n  logger.debug(\"GET_AUTHORIZATION_URL\", {\n    url,\n    cookies,\n    provider\n  });\n  return {\n    redirect: url,\n    cookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/authorization-url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/callback.js":
/*!***********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/callback.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = oAuthCallback;\nvar _openidClient = __webpack_require__(/*! openid-client */ \"(rsc)/./node_modules/openid-client/lib/index.js\");\nvar _client = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client.js\");\nvar _clientLegacy = __webpack_require__(/*! ./client-legacy */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js\");\nvar _checks = _interopRequireWildcard(__webpack_require__(/*! ./checks */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js\"));\nvar _errors = __webpack_require__(/*! ../../errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function oAuthCallback(params) {\n  var _body$error, _provider$version;\n  const {\n    options,\n    query,\n    body,\n    method,\n    cookies\n  } = params;\n  const {\n    logger,\n    provider\n  } = options;\n  const errorMessage = (_body$error = body === null || body === void 0 ? void 0 : body.error) !== null && _body$error !== void 0 ? _body$error : query === null || query === void 0 ? void 0 : query.error;\n  if (errorMessage) {\n    const error = new Error(errorMessage);\n    logger.error(\"OAUTH_CALLBACK_HANDLER_ERROR\", {\n      error,\n      error_description: query === null || query === void 0 ? void 0 : query.error_description,\n      providerId: provider.id\n    });\n    logger.debug(\"OAUTH_CALLBACK_HANDLER_ERROR\", {\n      body\n    });\n    throw error;\n  }\n  if ((_provider$version = provider.version) !== null && _provider$version !== void 0 && _provider$version.startsWith(\"1.\")) {\n    try {\n      const client = await (0, _clientLegacy.oAuth1Client)(options);\n      const {\n        oauth_token,\n        oauth_verifier\n      } = query !== null && query !== void 0 ? query : {};\n      const tokens = await client.getOAuthAccessToken(oauth_token, _clientLegacy.oAuth1TokenStore.get(oauth_token), oauth_verifier);\n      let profile = await client.get(provider.profileUrl, tokens.oauth_token, tokens.oauth_token_secret);\n      if (typeof profile === \"string\") {\n        profile = JSON.parse(profile);\n      }\n      const newProfile = await getProfile({\n        profile,\n        tokens,\n        provider,\n        logger\n      });\n      return {\n        ...newProfile,\n        cookies: []\n      };\n    } catch (error) {\n      logger.error(\"OAUTH_V1_GET_ACCESS_TOKEN_ERROR\", error);\n      throw error;\n    }\n  }\n  if (query !== null && query !== void 0 && query.oauth_token) _clientLegacy.oAuth1TokenStore.delete(query.oauth_token);\n  try {\n    var _provider$token, _provider$token2, _provider$userinfo;\n    const client = await (0, _client.openidClient)(options);\n    let tokens;\n    const checks = {};\n    const resCookies = [];\n    await _checks.state.use(cookies, resCookies, options, checks);\n    await _checks.pkce.use(cookies, resCookies, options, checks);\n    await _checks.nonce.use(cookies, resCookies, options, checks);\n    const params = {\n      ...client.callbackParams({\n        url: `http://n?${new URLSearchParams(query)}`,\n        body,\n        method\n      }),\n      ...((_provider$token = provider.token) === null || _provider$token === void 0 ? void 0 : _provider$token.params)\n    };\n    if ((_provider$token2 = provider.token) !== null && _provider$token2 !== void 0 && _provider$token2.request) {\n      const response = await provider.token.request({\n        provider,\n        params,\n        checks,\n        client\n      });\n      tokens = new _openidClient.TokenSet(response.tokens);\n    } else if (provider.idToken) {\n      tokens = await client.callback(provider.callbackUrl, params, checks);\n    } else {\n      tokens = await client.oauthCallback(provider.callbackUrl, params, checks);\n    }\n    if (Array.isArray(tokens.scope)) {\n      tokens.scope = tokens.scope.join(\" \");\n    }\n    let profile;\n    if ((_provider$userinfo = provider.userinfo) !== null && _provider$userinfo !== void 0 && _provider$userinfo.request) {\n      profile = await provider.userinfo.request({\n        provider,\n        tokens,\n        client\n      });\n    } else if (provider.idToken) {\n      profile = tokens.claims();\n    } else {\n      var _provider$userinfo2;\n      profile = await client.userinfo(tokens, {\n        params: (_provider$userinfo2 = provider.userinfo) === null || _provider$userinfo2 === void 0 ? void 0 : _provider$userinfo2.params\n      });\n    }\n    const profileResult = await getProfile({\n      profile,\n      provider,\n      tokens,\n      logger\n    });\n    return {\n      ...profileResult,\n      cookies: resCookies\n    };\n  } catch (error) {\n    throw new _errors.OAuthCallbackError(error);\n  }\n}\nasync function getProfile({\n  profile: OAuthProfile,\n  tokens,\n  provider,\n  logger\n}) {\n  try {\n    var _profile$email;\n    logger.debug(\"PROFILE_DATA\", {\n      OAuthProfile\n    });\n    const profile = await provider.profile(OAuthProfile, tokens);\n    profile.email = (_profile$email = profile.email) === null || _profile$email === void 0 ? void 0 : _profile$email.toLowerCase();\n    if (!profile.id) throw new TypeError(`Profile id is missing in ${provider.name} OAuth profile response`);\n    return {\n      profile,\n      account: {\n        provider: provider.id,\n        type: provider.type,\n        providerAccountId: profile.id.toString(),\n        ...tokens\n      },\n      OAuthProfile\n    };\n  } catch (error) {\n    logger.error(\"OAUTH_PARSE_PROFILE_ERROR\", {\n      error: error,\n      OAuthProfile\n    });\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/callback.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/checks.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.pkce = exports.nonce = exports.PKCE_CODE_CHALLENGE_METHOD = void 0;\nexports.signCookie = signCookie;\nexports.state = void 0;\nvar _openidClient = __webpack_require__(/*! openid-client */ \"(rsc)/./node_modules/openid-client/lib/index.js\");\nvar jwt = _interopRequireWildcard(__webpack_require__(/*! ../../../jwt */ \"(rsc)/./node_modules/next-auth/jwt/index.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function signCookie(type, value, maxAge, options) {\n  const {\n    cookies,\n    logger\n  } = options;\n  logger.debug(`CREATE_${type.toUpperCase()}`, {\n    value,\n    maxAge\n  });\n  const {\n    name\n  } = cookies[type];\n  const expires = new Date();\n  expires.setTime(expires.getTime() + maxAge * 1000);\n  return {\n    name,\n    value: await jwt.encode({\n      ...options.jwt,\n      maxAge,\n      token: {\n        value\n      },\n      salt: name\n    }),\n    options: {\n      ...cookies[type].options,\n      expires\n    }\n  };\n}\nconst PKCE_MAX_AGE = 60 * 15;\nconst PKCE_CODE_CHALLENGE_METHOD = exports.PKCE_CODE_CHALLENGE_METHOD = \"S256\";\nconst pkce = exports.pkce = {\n  async create(options, cookies, resParams) {\n    var _options$provider, _options$cookies$pkce;\n    if (!((_options$provider = options.provider) !== null && _options$provider !== void 0 && (_options$provider = _options$provider.checks) !== null && _options$provider !== void 0 && _options$provider.includes(\"pkce\"))) return;\n    const code_verifier = _openidClient.generators.codeVerifier();\n    const value = _openidClient.generators.codeChallenge(code_verifier);\n    resParams.code_challenge = value;\n    resParams.code_challenge_method = PKCE_CODE_CHALLENGE_METHOD;\n    const maxAge = (_options$cookies$pkce = options.cookies.pkceCodeVerifier.options.maxAge) !== null && _options$cookies$pkce !== void 0 ? _options$cookies$pkce : PKCE_MAX_AGE;\n    cookies.push(await signCookie(\"pkceCodeVerifier\", code_verifier, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider2;\n    if (!((_options$provider2 = options.provider) !== null && _options$provider2 !== void 0 && (_options$provider2 = _options$provider2.checks) !== null && _options$provider2 !== void 0 && _options$provider2.includes(\"pkce\"))) return;\n    const codeVerifier = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.pkceCodeVerifier.name];\n    if (!codeVerifier) throw new TypeError(\"PKCE code_verifier cookie was missing.\");\n    const {\n      name\n    } = options.cookies.pkceCodeVerifier;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: codeVerifier,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"PKCE code_verifier value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.pkceCodeVerifier.options,\n        maxAge: 0\n      }\n    });\n    checks.code_verifier = value.value;\n  }\n};\nconst STATE_MAX_AGE = 60 * 15;\nconst state = exports.state = {\n  async create(options, cookies, resParams) {\n    var _options$provider$che, _options$cookies$stat;\n    if (!((_options$provider$che = options.provider.checks) !== null && _options$provider$che !== void 0 && _options$provider$che.includes(\"state\"))) return;\n    const value = _openidClient.generators.state();\n    resParams.state = value;\n    const maxAge = (_options$cookies$stat = options.cookies.state.options.maxAge) !== null && _options$cookies$stat !== void 0 ? _options$cookies$stat : STATE_MAX_AGE;\n    cookies.push(await signCookie(\"state\", value, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider$che2;\n    if (!((_options$provider$che2 = options.provider.checks) !== null && _options$provider$che2 !== void 0 && _options$provider$che2.includes(\"state\"))) return;\n    const state = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.state.name];\n    if (!state) throw new TypeError(\"State cookie was missing.\");\n    const {\n      name\n    } = options.cookies.state;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: state,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"State value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.state.options,\n        maxAge: 0\n      }\n    });\n    checks.state = value.value;\n  }\n};\nconst NONCE_MAX_AGE = 60 * 15;\nconst nonce = exports.nonce = {\n  async create(options, cookies, resParams) {\n    var _options$provider$che3, _options$cookies$nonc;\n    if (!((_options$provider$che3 = options.provider.checks) !== null && _options$provider$che3 !== void 0 && _options$provider$che3.includes(\"nonce\"))) return;\n    const value = _openidClient.generators.nonce();\n    resParams.nonce = value;\n    const maxAge = (_options$cookies$nonc = options.cookies.nonce.options.maxAge) !== null && _options$cookies$nonc !== void 0 ? _options$cookies$nonc : NONCE_MAX_AGE;\n    cookies.push(await signCookie(\"nonce\", value, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider3;\n    if (!((_options$provider3 = options.provider) !== null && _options$provider3 !== void 0 && (_options$provider3 = _options$provider3.checks) !== null && _options$provider3 !== void 0 && _options$provider3.includes(\"nonce\"))) return;\n    const nonce = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.nonce.name];\n    if (!nonce) throw new TypeError(\"Nonce cookie was missing.\");\n    const {\n      name\n    } = options.cookies.nonce;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: nonce,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"Nonce value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.nonce.options,\n        maxAge: 0\n      }\n    });\n    checks.nonce = value.value;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js":
/*!****************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/client-legacy.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.oAuth1Client = oAuth1Client;\nexports.oAuth1TokenStore = void 0;\nvar _oauth = __webpack_require__(/*! oauth */ \"(rsc)/./node_modules/oauth/index.js\");\nfunction oAuth1Client(options) {\n  var _provider$version, _provider$encoding;\n  const provider = options.provider;\n  const oauth1Client = new _oauth.OAuth(provider.requestTokenUrl, provider.accessTokenUrl, provider.clientId, provider.clientSecret, (_provider$version = provider.version) !== null && _provider$version !== void 0 ? _provider$version : \"1.0\", provider.callbackUrl, (_provider$encoding = provider.encoding) !== null && _provider$encoding !== void 0 ? _provider$encoding : \"HMAC-SHA1\");\n  const originalGet = oauth1Client.get.bind(oauth1Client);\n  oauth1Client.get = async (...args) => {\n    return await new Promise((resolve, reject) => {\n      originalGet(...args, (error, result) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve(result);\n      });\n    });\n  };\n  const originalGetOAuth1AccessToken = oauth1Client.getOAuthAccessToken.bind(oauth1Client);\n  oauth1Client.getOAuthAccessToken = async (...args) => {\n    return await new Promise((resolve, reject) => {\n      originalGetOAuth1AccessToken(...args, (error, oauth_token, oauth_token_secret) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve({\n          oauth_token,\n          oauth_token_secret\n        });\n      });\n    });\n  };\n  const originalGetOAuthRequestToken = oauth1Client.getOAuthRequestToken.bind(oauth1Client);\n  oauth1Client.getOAuthRequestToken = async (params = {}) => {\n    return await new Promise((resolve, reject) => {\n      originalGetOAuthRequestToken(params, (error, oauth_token, oauth_token_secret, params) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve({\n          oauth_token,\n          oauth_token_secret,\n          params\n        });\n      });\n    });\n  };\n  return oauth1Client;\n}\nconst oAuth1TokenStore = exports.oAuth1TokenStore = new Map();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/client.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/client.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.openidClient = openidClient;\nvar _openidClient = __webpack_require__(/*! openid-client */ \"(rsc)/./node_modules/openid-client/lib/index.js\");\nasync function openidClient(options) {\n  const provider = options.provider;\n  if (provider.httpOptions) _openidClient.custom.setHttpOptionsDefaults(provider.httpOptions);\n  let issuer;\n  if (provider.wellKnown) {\n    issuer = await _openidClient.Issuer.discover(provider.wellKnown);\n  } else {\n    var _provider$authorizati, _provider$token, _provider$userinfo;\n    issuer = new _openidClient.Issuer({\n      issuer: provider.issuer,\n      authorization_endpoint: (_provider$authorizati = provider.authorization) === null || _provider$authorizati === void 0 ? void 0 : _provider$authorizati.url,\n      token_endpoint: (_provider$token = provider.token) === null || _provider$token === void 0 ? void 0 : _provider$token.url,\n      userinfo_endpoint: (_provider$userinfo = provider.userinfo) === null || _provider$userinfo === void 0 ? void 0 : _provider$userinfo.url,\n      jwks_uri: provider.jwks_endpoint\n    });\n  }\n  const client = new issuer.Client({\n    client_id: provider.clientId,\n    client_secret: provider.clientSecret,\n    redirect_uris: [provider.callbackUrl],\n    ...provider.client\n  }, provider.jwks);\n  client[_openidClient.custom.clock_tolerance] = 10;\n  return client;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/providers.js":
/*!******************************************************!*\
  !*** ./node_modules/next-auth/core/lib/providers.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseProviders;\nvar _merge = __webpack_require__(/*! ../../utils/merge */ \"(rsc)/./node_modules/next-auth/utils/merge.js\");\nfunction parseProviders(params) {\n  const {\n    url,\n    providerId\n  } = params;\n  const providers = params.providers.map(({\n    options: userOptions,\n    ...rest\n  }) => {\n    var _ref;\n    if (rest.type === \"oauth\") {\n      var _normalizedUserOption;\n      const normalizedOptions = normalizeOAuthOptions(rest);\n      const normalizedUserOptions = normalizeOAuthOptions(userOptions, true);\n      const id = (_normalizedUserOption = normalizedUserOptions === null || normalizedUserOptions === void 0 ? void 0 : normalizedUserOptions.id) !== null && _normalizedUserOption !== void 0 ? _normalizedUserOption : rest.id;\n      return (0, _merge.merge)(normalizedOptions, {\n        ...normalizedUserOptions,\n        signinUrl: `${url}/signin/${id}`,\n        callbackUrl: `${url}/callback/${id}`\n      });\n    }\n    const id = (_ref = userOptions === null || userOptions === void 0 ? void 0 : userOptions.id) !== null && _ref !== void 0 ? _ref : rest.id;\n    return (0, _merge.merge)(rest, {\n      ...userOptions,\n      signinUrl: `${url}/signin/${id}`,\n      callbackUrl: `${url}/callback/${id}`\n    });\n  });\n  return {\n    providers,\n    provider: providers.find(({\n      id\n    }) => id === providerId)\n  };\n}\nfunction normalizeOAuthOptions(oauthOptions, isUserOptions = false) {\n  var _normalized$version;\n  if (!oauthOptions) return;\n  const normalized = Object.entries(oauthOptions).reduce((acc, [key, value]) => {\n    if ([\"authorization\", \"token\", \"userinfo\"].includes(key) && typeof value === \"string\") {\n      var _url$searchParams;\n      const url = new URL(value);\n      acc[key] = {\n        url: `${url.origin}${url.pathname}`,\n        params: Object.fromEntries((_url$searchParams = url.searchParams) !== null && _url$searchParams !== void 0 ? _url$searchParams : [])\n      };\n    } else {\n      acc[key] = value;\n    }\n    return acc;\n  }, {});\n  if (!isUserOptions && !((_normalized$version = normalized.version) !== null && _normalized$version !== void 0 && _normalized$version.startsWith(\"1.\"))) {\n    var _ref2, _normalized$idToken, _normalized$wellKnown, _normalized$authoriza;\n    normalized.idToken = Boolean((_ref2 = (_normalized$idToken = normalized.idToken) !== null && _normalized$idToken !== void 0 ? _normalized$idToken : (_normalized$wellKnown = normalized.wellKnown) === null || _normalized$wellKnown === void 0 ? void 0 : _normalized$wellKnown.includes(\"openid-configuration\")) !== null && _ref2 !== void 0 ? _ref2 : (_normalized$authoriza = normalized.authorization) === null || _normalized$authoriza === void 0 || (_normalized$authoriza = _normalized$authoriza.params) === null || _normalized$authoriza === void 0 || (_normalized$authoriza = _normalized$authoriza.scope) === null || _normalized$authoriza === void 0 ? void 0 : _normalized$authoriza.includes(\"openid\"));\n    if (!normalized.checks) normalized.checks = [\"state\"];\n  }\n  return normalized;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/providers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/utils.js":
/*!**************************************************!*\
  !*** ./node_modules/next-auth/core/lib/utils.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createSecret = createSecret;\nexports.fromDate = fromDate;\nexports.hashToken = hashToken;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nfunction fromDate(time, date = Date.now()) {\n  return new Date(date + time * 1000);\n}\nfunction hashToken(token, options) {\n  var _provider$secret;\n  const {\n    provider,\n    secret\n  } = options;\n  return (0, _crypto.createHash)(\"sha256\").update(`${token}${(_provider$secret = provider.secret) !== null && _provider$secret !== void 0 ? _provider$secret : secret}`).digest(\"hex\");\n}\nfunction createSecret(params) {\n  var _authOptions$secret;\n  const {\n    authOptions,\n    url\n  } = params;\n  return (_authOptions$secret = authOptions.secret) !== null && _authOptions$secret !== void 0 ? _authOptions$secret : (0, _crypto.createHash)(\"sha256\").update(JSON.stringify({\n    ...url,\n    ...authOptions\n  })).digest(\"hex\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLG9CQUFvQjtBQUNwQixnQkFBZ0I7QUFDaEIsaUJBQWlCO0FBQ2pCLGNBQWMsbUJBQU8sQ0FBQyxzQkFBUTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLHFEQUFxRCxNQUFNLEVBQUUseUdBQXlHO0FBQ3RLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL3V0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5jcmVhdGVTZWNyZXQgPSBjcmVhdGVTZWNyZXQ7XG5leHBvcnRzLmZyb21EYXRlID0gZnJvbURhdGU7XG5leHBvcnRzLmhhc2hUb2tlbiA9IGhhc2hUb2tlbjtcbnZhciBfY3J5cHRvID0gcmVxdWlyZShcImNyeXB0b1wiKTtcbmZ1bmN0aW9uIGZyb21EYXRlKHRpbWUsIGRhdGUgPSBEYXRlLm5vdygpKSB7XG4gIHJldHVybiBuZXcgRGF0ZShkYXRlICsgdGltZSAqIDEwMDApO1xufVxuZnVuY3Rpb24gaGFzaFRva2VuKHRva2VuLCBvcHRpb25zKSB7XG4gIHZhciBfcHJvdmlkZXIkc2VjcmV0O1xuICBjb25zdCB7XG4gICAgcHJvdmlkZXIsXG4gICAgc2VjcmV0XG4gIH0gPSBvcHRpb25zO1xuICByZXR1cm4gKDAsIF9jcnlwdG8uY3JlYXRlSGFzaCkoXCJzaGEyNTZcIikudXBkYXRlKGAke3Rva2VufSR7KF9wcm92aWRlciRzZWNyZXQgPSBwcm92aWRlci5zZWNyZXQpICE9PSBudWxsICYmIF9wcm92aWRlciRzZWNyZXQgIT09IHZvaWQgMCA/IF9wcm92aWRlciRzZWNyZXQgOiBzZWNyZXR9YCkuZGlnZXN0KFwiaGV4XCIpO1xufVxuZnVuY3Rpb24gY3JlYXRlU2VjcmV0KHBhcmFtcykge1xuICB2YXIgX2F1dGhPcHRpb25zJHNlY3JldDtcbiAgY29uc3Qge1xuICAgIGF1dGhPcHRpb25zLFxuICAgIHVybFxuICB9ID0gcGFyYW1zO1xuICByZXR1cm4gKF9hdXRoT3B0aW9ucyRzZWNyZXQgPSBhdXRoT3B0aW9ucy5zZWNyZXQpICE9PSBudWxsICYmIF9hdXRoT3B0aW9ucyRzZWNyZXQgIT09IHZvaWQgMCA/IF9hdXRoT3B0aW9ucyRzZWNyZXQgOiAoMCwgX2NyeXB0by5jcmVhdGVIYXNoKShcInNoYTI1NlwiKS51cGRhdGUoSlNPTi5zdHJpbmdpZnkoe1xuICAgIC4uLnVybCxcbiAgICAuLi5hdXRoT3B0aW9uc1xuICB9KSkuZGlnZXN0KFwiaGV4XCIpO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/error.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/core/pages/error.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = ErrorPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js\");\nfunction ErrorPage(props) {\n  var _errors$error$toLower;\n  const {\n    url,\n    error = \"default\",\n    theme\n  } = props;\n  const signinPageUrl = `${url}/signin`;\n  const errors = {\n    default: {\n      status: 200,\n      heading: \"Error\",\n      message: (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n        className: \"site\",\n        href: url === null || url === void 0 ? void 0 : url.origin\n      }, url === null || url === void 0 ? void 0 : url.host))\n    },\n    configuration: {\n      status: 500,\n      heading: \"Server error\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"There is a problem with the server configuration.\"), (0, _preact.h)(\"p\", null, \"Check the server logs for more information.\"))\n    },\n    accessdenied: {\n      status: 403,\n      heading: \"Access Denied\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"You do not have permission to sign in.\"), (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n        className: \"button\",\n        href: signinPageUrl\n      }, \"Sign in\")))\n    },\n    verification: {\n      status: 403,\n      heading: \"Unable to sign in\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"The sign in link is no longer valid.\"), (0, _preact.h)(\"p\", null, \"It may have been used already or it may have expired.\")),\n      signin: (0, _preact.h)(\"a\", {\n        className: \"button\",\n        href: signinPageUrl\n      }, \"Sign in\")\n    }\n  };\n  const {\n    status,\n    heading,\n    message,\n    signin\n  } = (_errors$error$toLower = errors[error.toLowerCase()]) !== null && _errors$error$toLower !== void 0 ? _errors$error$toLower : errors.default;\n  return {\n    status,\n    html: (0, _preact.h)(\"div\", {\n      className: \"error\"\n    }, (theme === null || theme === void 0 ? void 0 : theme.brandColor) && (0, _preact.h)(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: `\n        :root {\n          --brand-color: ${theme === null || theme === void 0 ? void 0 : theme.brandColor}\n        }\n      `\n      }\n    }), (0, _preact.h)(\"div\", {\n      className: \"card\"\n    }, (theme === null || theme === void 0 ? void 0 : theme.logo) && (0, _preact.h)(\"img\", {\n      src: theme.logo,\n      alt: \"Logo\",\n      className: \"logo\"\n    }), (0, _preact.h)(\"h1\", null, heading), (0, _preact.h)(\"div\", {\n      className: \"message\"\n    }, message), signin))\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/index.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/core/pages/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = renderPage;\nvar _preactRenderToString = _interopRequireDefault(__webpack_require__(/*! preact-render-to-string */ \"(rsc)/./node_modules/next-auth/node_modules/preact-render-to-string/dist/index.js\"));\nvar _signin = _interopRequireDefault(__webpack_require__(/*! ./signin */ \"(rsc)/./node_modules/next-auth/core/pages/signin.js\"));\nvar _signout = _interopRequireDefault(__webpack_require__(/*! ./signout */ \"(rsc)/./node_modules/next-auth/core/pages/signout.js\"));\nvar _verifyRequest = _interopRequireDefault(__webpack_require__(/*! ./verify-request */ \"(rsc)/./node_modules/next-auth/core/pages/verify-request.js\"));\nvar _error = _interopRequireDefault(__webpack_require__(/*! ./error */ \"(rsc)/./node_modules/next-auth/core/pages/error.js\"));\nvar _css = _interopRequireDefault(__webpack_require__(/*! ../../css */ \"(rsc)/./node_modules/next-auth/css/index.js\"));\nfunction renderPage(params) {\n  const {\n    url,\n    theme,\n    query,\n    cookies\n  } = params;\n  function send({\n    html,\n    title,\n    status\n  }) {\n    var _theme$colorScheme;\n    return {\n      cookies,\n      status,\n      headers: [{\n        key: \"Content-Type\",\n        value: \"text/html\"\n      }],\n      body: `<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><style>${(0, _css.default)()}</style><title>${title}</title></head><body class=\"__next-auth-theme-${(_theme$colorScheme = theme === null || theme === void 0 ? void 0 : theme.colorScheme) !== null && _theme$colorScheme !== void 0 ? _theme$colorScheme : \"auto\"}\"><div class=\"page\">${(0, _preactRenderToString.default)(html)}</div></body></html>`\n    };\n  }\n  return {\n    signin(props) {\n      return send({\n        html: (0, _signin.default)({\n          csrfToken: params.csrfToken,\n          providers: params.providers,\n          callbackUrl: params.callbackUrl,\n          theme,\n          ...query,\n          ...props\n        }),\n        title: \"Sign In\"\n      });\n    },\n    signout(props) {\n      return send({\n        html: (0, _signout.default)({\n          csrfToken: params.csrfToken,\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Sign Out\"\n      });\n    },\n    verifyRequest(props) {\n      return send({\n        html: (0, _verifyRequest.default)({\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Verify Request\"\n      });\n    },\n    error(props) {\n      return send({\n        ...(0, _error.default)({\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Error\"\n      });\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/signin.js":
/*!*****************************************************!*\
  !*** ./node_modules/next-auth/core/pages/signin.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = SigninPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js\");\nvar _extends2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/extends */ \"(rsc)/./node_modules/@babel/runtime/helpers/extends.js\"));\nfunction hexToRgba(hex, alpha = 1) {\n  if (!hex) {\n    return;\n  }\n  hex = hex.replace(/^#/, \"\");\n  if (hex.length === 3) {\n    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];\n  }\n  const bigint = parseInt(hex, 16);\n  const r = bigint >> 16 & 255;\n  const g = bigint >> 8 & 255;\n  const b = bigint & 255;\n  alpha = Math.min(Math.max(alpha, 0), 1);\n  const rgba = `rgba(${r}, ${g}, ${b}, ${alpha})`;\n  return rgba;\n}\nfunction SigninPage(props) {\n  var _errors$errorType;\n  const {\n    csrfToken,\n    providers,\n    callbackUrl,\n    theme,\n    email,\n    error: errorType\n  } = props;\n  const providersToRender = providers.filter(provider => {\n    if (provider.type === \"oauth\" || provider.type === \"email\") {\n      return true;\n    } else if (provider.type === \"credentials\" && provider.credentials) {\n      return true;\n    }\n    return false;\n  });\n  if (typeof document !== \"undefined\" && theme.buttonText) {\n    document.documentElement.style.setProperty(\"--button-text-color\", theme.buttonText);\n  }\n  if (typeof document !== \"undefined\" && theme.brandColor) {\n    document.documentElement.style.setProperty(\"--brand-color\", theme.brandColor);\n  }\n  const errors = {\n    Signin: \"Try signing in with a different account.\",\n    OAuthSignin: \"Try signing in with a different account.\",\n    OAuthCallback: \"Try signing in with a different account.\",\n    OAuthCreateAccount: \"Try signing in with a different account.\",\n    EmailCreateAccount: \"Try signing in with a different account.\",\n    Callback: \"Try signing in with a different account.\",\n    OAuthAccountNotLinked: \"To confirm your identity, sign in with the same account you used originally.\",\n    EmailSignin: \"The e-mail could not be sent.\",\n    CredentialsSignin: \"Sign in failed. Check the details you provided are correct.\",\n    SessionRequired: \"Please sign in to access this page.\",\n    default: \"Unable to sign in.\"\n  };\n  const error = errorType && ((_errors$errorType = errors[errorType]) !== null && _errors$errorType !== void 0 ? _errors$errorType : errors.default);\n  const providerLogoPath = \"https://authjs.dev/img/providers\";\n  return (0, _preact.h)(\"div\", {\n    className: \"signin\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), theme.buttonText && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), error && (0, _preact.h)(\"div\", {\n    className: \"error\"\n  }, (0, _preact.h)(\"p\", null, error)), providersToRender.map((provider, i) => {\n    let bg, text, logo, logoDark, bgDark, textDark;\n    if (provider.type === \"oauth\") {\n      var _provider$style;\n      ;\n      ({\n        bg = \"\",\n        text = \"\",\n        logo = \"\",\n        bgDark = bg,\n        textDark = text,\n        logoDark = \"\"\n      } = (_provider$style = provider.style) !== null && _provider$style !== void 0 ? _provider$style : {});\n      logo = logo.startsWith(\"/\") ? `${providerLogoPath}${logo}` : logo;\n      logoDark = logoDark.startsWith(\"/\") ? `${providerLogoPath}${logoDark}` : logoDark || logo;\n      logoDark || (logoDark = logo);\n    }\n    return (0, _preact.h)(\"div\", {\n      key: provider.id,\n      className: \"provider\"\n    }, provider.type === \"oauth\" && (0, _preact.h)(\"form\", {\n      action: provider.signinUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), callbackUrl && (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"callbackUrl\",\n      value: callbackUrl\n    }), (0, _preact.h)(\"button\", {\n      type: \"submit\",\n      className: \"button\",\n      style: {\n        \"--provider-bg\": bg,\n        \"--provider-dark-bg\": bgDark,\n        \"--provider-color\": text,\n        \"--provider-dark-color\": textDark,\n        \"--provider-bg-hover\": hexToRgba(bg, 0.8),\n        \"--provider-dark-bg-hover\": hexToRgba(bgDark, 0.8)\n      }\n    }, logo && (0, _preact.h)(\"img\", {\n      loading: \"lazy\",\n      height: 24,\n      width: 24,\n      id: \"provider-logo\",\n      src: `${logo.startsWith(\"/\") ? providerLogoPath : \"\"}${logo}`\n    }), logoDark && (0, _preact.h)(\"img\", {\n      loading: \"lazy\",\n      height: 24,\n      width: 24,\n      id: \"provider-logo-dark\",\n      src: `${logo.startsWith(\"/\") ? providerLogoPath : \"\"}${logoDark}`\n    }), (0, _preact.h)(\"span\", null, \"Sign in with \", provider.name))), (provider.type === \"email\" || provider.type === \"credentials\") && i > 0 && providersToRender[i - 1].type !== \"email\" && providersToRender[i - 1].type !== \"credentials\" && (0, _preact.h)(\"hr\", null), provider.type === \"email\" && (0, _preact.h)(\"form\", {\n      action: provider.signinUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), (0, _preact.h)(\"label\", {\n      className: \"section-header\",\n      htmlFor: `input-email-for-${provider.id}-provider`\n    }, \"Email\"), (0, _preact.h)(\"input\", {\n      id: `input-email-for-${provider.id}-provider`,\n      autoFocus: true,\n      type: \"email\",\n      name: \"email\",\n      value: email,\n      placeholder: \"<EMAIL>\",\n      required: true\n    }), (0, _preact.h)(\"button\", {\n      id: \"submitButton\",\n      type: \"submit\"\n    }, \"Sign in with \", provider.name)), provider.type === \"credentials\" && (0, _preact.h)(\"form\", {\n      action: provider.callbackUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), Object.keys(provider.credentials).map(credential => {\n      var _provider$credentials, _provider$credentials2, _provider$credentials3;\n      return (0, _preact.h)(\"div\", {\n        key: `input-group-${provider.id}`\n      }, (0, _preact.h)(\"label\", {\n        className: \"section-header\",\n        htmlFor: `input-${credential}-for-${provider.id}-provider`\n      }, (_provider$credentials = provider.credentials[credential].label) !== null && _provider$credentials !== void 0 ? _provider$credentials : credential), (0, _preact.h)(\"input\", (0, _extends2.default)({\n        name: credential,\n        id: `input-${credential}-for-${provider.id}-provider`,\n        type: (_provider$credentials2 = provider.credentials[credential].type) !== null && _provider$credentials2 !== void 0 ? _provider$credentials2 : \"text\",\n        placeholder: (_provider$credentials3 = provider.credentials[credential].placeholder) !== null && _provider$credentials3 !== void 0 ? _provider$credentials3 : \"\"\n      }, provider.credentials[credential])));\n    }), (0, _preact.h)(\"button\", {\n      type: \"submit\"\n    }, \"Sign in with \", provider.name)), (provider.type === \"email\" || provider.type === \"credentials\") && i + 1 < providersToRender.length && (0, _preact.h)(\"hr\", null));\n  })));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/signin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/signout.js":
/*!******************************************************!*\
  !*** ./node_modules/next-auth/core/pages/signout.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = SignoutPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js\");\nfunction SignoutPage(props) {\n  const {\n    url,\n    csrfToken,\n    theme\n  } = props;\n  return (0, _preact.h)(\"div\", {\n    className: \"signout\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), theme.buttonText && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), (0, _preact.h)(\"h1\", null, \"Signout\"), (0, _preact.h)(\"p\", null, \"Are you sure you want to sign out?\"), (0, _preact.h)(\"form\", {\n    action: `${url}/signout`,\n    method: \"POST\"\n  }, (0, _preact.h)(\"input\", {\n    type: \"hidden\",\n    name: \"csrfToken\",\n    value: csrfToken\n  }), (0, _preact.h)(\"button\", {\n    id: \"submitButton\",\n    type: \"submit\"\n  }, \"Sign out\"))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/signout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/verify-request.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-auth/core/pages/verify-request.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = VerifyRequestPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js\");\nfunction VerifyRequestPage(props) {\n  const {\n    url,\n    theme\n  } = props;\n  return (0, _preact.h)(\"div\", {\n    className: \"verify-request\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), (0, _preact.h)(\"h1\", null, \"Check your email\"), (0, _preact.h)(\"p\", null, \"A sign in link has been sent to your email address.\"), (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n    className: \"site\",\n    href: url.origin\n  }, url.host))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvcGFnZXMvdmVyaWZ5LXJlcXVlc3QuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZixjQUFjLG1CQUFPLENBQUMsaUZBQVE7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtYXV0aC9jb3JlL3BhZ2VzL3ZlcmlmeS1yZXF1ZXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gVmVyaWZ5UmVxdWVzdFBhZ2U7XG52YXIgX3ByZWFjdCA9IHJlcXVpcmUoXCJwcmVhY3RcIik7XG5mdW5jdGlvbiBWZXJpZnlSZXF1ZXN0UGFnZShwcm9wcykge1xuICBjb25zdCB7XG4gICAgdXJsLFxuICAgIHRoZW1lXG4gIH0gPSBwcm9wcztcbiAgcmV0dXJuICgwLCBfcHJlYWN0LmgpKFwiZGl2XCIsIHtcbiAgICBjbGFzc05hbWU6IFwidmVyaWZ5LXJlcXVlc3RcIlxuICB9LCB0aGVtZS5icmFuZENvbG9yICYmICgwLCBfcHJlYWN0LmgpKFwic3R5bGVcIiwge1xuICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICBfX2h0bWw6IGBcbiAgICAgICAgOnJvb3Qge1xuICAgICAgICAgIC0tYnJhbmQtY29sb3I6ICR7dGhlbWUuYnJhbmRDb2xvcn1cbiAgICAgICAgfVxuICAgICAgYFxuICAgIH1cbiAgfSksICgwLCBfcHJlYWN0LmgpKFwiZGl2XCIsIHtcbiAgICBjbGFzc05hbWU6IFwiY2FyZFwiXG4gIH0sIHRoZW1lLmxvZ28gJiYgKDAsIF9wcmVhY3QuaCkoXCJpbWdcIiwge1xuICAgIHNyYzogdGhlbWUubG9nbyxcbiAgICBhbHQ6IFwiTG9nb1wiLFxuICAgIGNsYXNzTmFtZTogXCJsb2dvXCJcbiAgfSksICgwLCBfcHJlYWN0LmgpKFwiaDFcIiwgbnVsbCwgXCJDaGVjayB5b3VyIGVtYWlsXCIpLCAoMCwgX3ByZWFjdC5oKShcInBcIiwgbnVsbCwgXCJBIHNpZ24gaW4gbGluayBoYXMgYmVlbiBzZW50IHRvIHlvdXIgZW1haWwgYWRkcmVzcy5cIiksICgwLCBfcHJlYWN0LmgpKFwicFwiLCBudWxsLCAoMCwgX3ByZWFjdC5oKShcImFcIiwge1xuICAgIGNsYXNzTmFtZTogXCJzaXRlXCIsXG4gICAgaHJlZjogdXJsLm9yaWdpblxuICB9LCB1cmwuaG9zdCkpKSk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/verify-request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/callback.js":
/*!********************************************************!*\
  !*** ./node_modules/next-auth/core/routes/callback.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = callback;\nvar _callback = _interopRequireDefault(__webpack_require__(/*! ../lib/oauth/callback */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/callback.js\"));\nvar _callbackHandler = _interopRequireDefault(__webpack_require__(/*! ../lib/callback-handler */ \"(rsc)/./node_modules/next-auth/core/lib/callback-handler.js\"));\nvar _utils = __webpack_require__(/*! ../lib/utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nvar _getUserFromEmail = _interopRequireDefault(__webpack_require__(/*! ../lib/email/getUserFromEmail */ \"(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js\"));\nasync function callback(params) {\n  const {\n    options,\n    query,\n    body,\n    method,\n    headers,\n    sessionStore\n  } = params;\n  const {\n    provider,\n    adapter,\n    url,\n    callbackUrl,\n    pages,\n    jwt,\n    events,\n    callbacks,\n    session: {\n      strategy: sessionStrategy,\n      maxAge: sessionMaxAge\n    },\n    logger\n  } = options;\n  const cookies = [];\n  const useJwtSession = sessionStrategy === \"jwt\";\n  if (provider.type === \"oauth\") {\n    try {\n      const {\n        profile,\n        account,\n        OAuthProfile,\n        cookies: oauthCookies\n      } = await (0, _callback.default)({\n        query,\n        body,\n        method,\n        options,\n        cookies: params.cookies\n      });\n      if (oauthCookies.length) cookies.push(...oauthCookies);\n      try {\n        var _events$signIn;\n        logger.debug(\"OAUTH_CALLBACK_RESPONSE\", {\n          profile,\n          account,\n          OAuthProfile\n        });\n        if (!profile || !account || !OAuthProfile) {\n          return {\n            redirect: `${url}/signin`,\n            cookies\n          };\n        }\n        let userOrProfile = profile;\n        if (adapter) {\n          const {\n            getUserByAccount\n          } = adapter;\n          const userByAccount = await getUserByAccount({\n            providerAccountId: account.providerAccountId,\n            provider: provider.id\n          });\n          if (userByAccount) userOrProfile = userByAccount;\n        }\n        try {\n          const isAllowed = await callbacks.signIn({\n            user: userOrProfile,\n            account,\n            profile: OAuthProfile\n          });\n          if (!isAllowed) {\n            return {\n              redirect: `${url}/error?error=AccessDenied`,\n              cookies\n            };\n          } else if (typeof isAllowed === \"string\") {\n            return {\n              redirect: isAllowed,\n              cookies\n            };\n          }\n        } catch (error) {\n          return {\n            redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n            cookies\n          };\n        }\n        const {\n          user,\n          session,\n          isNewUser\n        } = await (0, _callbackHandler.default)({\n          sessionToken: sessionStore.value,\n          profile,\n          account,\n          options\n        });\n        if (useJwtSession) {\n          var _user$id;\n          const defaultToken = {\n            name: user.name,\n            email: user.email,\n            picture: user.image,\n            sub: (_user$id = user.id) === null || _user$id === void 0 ? void 0 : _user$id.toString()\n          };\n          const token = await callbacks.jwt({\n            token: defaultToken,\n            user,\n            account,\n            profile: OAuthProfile,\n            isNewUser,\n            trigger: isNewUser ? \"signUp\" : \"signIn\"\n          });\n          const newToken = await jwt.encode({\n            ...jwt,\n            token\n          });\n          const cookieExpires = new Date();\n          cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n          const sessionCookies = sessionStore.chunk(newToken, {\n            expires: cookieExpires\n          });\n          cookies.push(...sessionCookies);\n        } else {\n          cookies.push({\n            name: options.cookies.sessionToken.name,\n            value: session.sessionToken,\n            options: {\n              ...options.cookies.sessionToken.options,\n              expires: session.expires\n            }\n          });\n        }\n        await ((_events$signIn = events.signIn) === null || _events$signIn === void 0 ? void 0 : _events$signIn.call(events, {\n          user,\n          account,\n          profile,\n          isNewUser\n        }));\n        if (isNewUser && pages.newUser) {\n          return {\n            redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(callbackUrl)}`,\n            cookies\n          };\n        }\n        return {\n          redirect: callbackUrl,\n          cookies\n        };\n      } catch (error) {\n        if (error.name === \"AccountNotLinkedError\") {\n          return {\n            redirect: `${url}/error?error=OAuthAccountNotLinked`,\n            cookies\n          };\n        } else if (error.name === \"CreateUserError\") {\n          return {\n            redirect: `${url}/error?error=OAuthCreateAccount`,\n            cookies\n          };\n        }\n        logger.error(\"OAUTH_CALLBACK_HANDLER_ERROR\", error);\n        return {\n          redirect: `${url}/error?error=Callback`,\n          cookies\n        };\n      }\n    } catch (error) {\n      if (error.name === \"OAuthCallbackError\") {\n        logger.error(\"OAUTH_CALLBACK_ERROR\", {\n          error: error,\n          providerId: provider.id\n        });\n        return {\n          redirect: `${url}/error?error=OAuthCallback`,\n          cookies\n        };\n      }\n      logger.error(\"OAUTH_CALLBACK_ERROR\", error);\n      return {\n        redirect: `${url}/error?error=Callback`,\n        cookies\n      };\n    }\n  } else if (provider.type === \"email\") {\n    try {\n      var _events$signIn2;\n      const paramToken = query === null || query === void 0 ? void 0 : query.token;\n      const paramIdentifier = query === null || query === void 0 ? void 0 : query.email;\n      if (!paramToken) {\n        return {\n          redirect: `${url}/error?error=configuration`,\n          cookies\n        };\n      }\n      const invite = await adapter.useVerificationToken({\n        identifier: paramIdentifier,\n        token: (0, _utils.hashToken)(paramToken, options)\n      });\n      const invalidInvite = !invite || invite.expires.valueOf() < Date.now() || paramIdentifier && invite.identifier !== paramIdentifier;\n      if (invalidInvite) {\n        return {\n          redirect: `${url}/error?error=Verification`,\n          cookies\n        };\n      }\n      const profile = await (0, _getUserFromEmail.default)({\n        email: invite.identifier,\n        adapter\n      });\n      const account = {\n        providerAccountId: profile.email,\n        type: \"email\",\n        provider: provider.id\n      };\n      try {\n        const signInCallbackResponse = await callbacks.signIn({\n          user: profile,\n          account\n        });\n        if (!signInCallbackResponse) {\n          return {\n            redirect: `${url}/error?error=AccessDenied`,\n            cookies\n          };\n        } else if (typeof signInCallbackResponse === \"string\") {\n          return {\n            redirect: signInCallbackResponse,\n            cookies\n          };\n        }\n      } catch (error) {\n        return {\n          redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n          cookies\n        };\n      }\n      const {\n        user,\n        session,\n        isNewUser\n      } = await (0, _callbackHandler.default)({\n        sessionToken: sessionStore.value,\n        profile,\n        account,\n        options\n      });\n      if (useJwtSession) {\n        var _user$id2;\n        const defaultToken = {\n          name: user.name,\n          email: user.email,\n          picture: user.image,\n          sub: (_user$id2 = user.id) === null || _user$id2 === void 0 ? void 0 : _user$id2.toString()\n        };\n        const token = await callbacks.jwt({\n          token: defaultToken,\n          user,\n          account,\n          isNewUser,\n          trigger: isNewUser ? \"signUp\" : \"signIn\"\n        });\n        const newToken = await jwt.encode({\n          ...jwt,\n          token\n        });\n        const cookieExpires = new Date();\n        cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n        const sessionCookies = sessionStore.chunk(newToken, {\n          expires: cookieExpires\n        });\n        cookies.push(...sessionCookies);\n      } else {\n        cookies.push({\n          name: options.cookies.sessionToken.name,\n          value: session.sessionToken,\n          options: {\n            ...options.cookies.sessionToken.options,\n            expires: session.expires\n          }\n        });\n      }\n      await ((_events$signIn2 = events.signIn) === null || _events$signIn2 === void 0 ? void 0 : _events$signIn2.call(events, {\n        user,\n        account,\n        isNewUser\n      }));\n      if (isNewUser && pages.newUser) {\n        return {\n          redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(callbackUrl)}`,\n          cookies\n        };\n      }\n      return {\n        redirect: callbackUrl,\n        cookies\n      };\n    } catch (error) {\n      if (error.name === \"CreateUserError\") {\n        return {\n          redirect: `${url}/error?error=EmailCreateAccount`,\n          cookies\n        };\n      }\n      logger.error(\"CALLBACK_EMAIL_ERROR\", error);\n      return {\n        redirect: `${url}/error?error=Callback`,\n        cookies\n      };\n    }\n  } else if (provider.type === \"credentials\" && method === \"POST\") {\n    var _user$id3, _events$signIn3;\n    const credentials = body;\n    let user;\n    try {\n      user = await provider.authorize(credentials, {\n        query,\n        body,\n        headers,\n        method\n      });\n      if (!user) {\n        return {\n          status: 401,\n          redirect: `${url}/error?${new URLSearchParams({\n            error: \"CredentialsSignin\",\n            provider: provider.id\n          })}`,\n          cookies\n        };\n      }\n    } catch (error) {\n      return {\n        status: 401,\n        redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n        cookies\n      };\n    }\n    const account = {\n      providerAccountId: user.id,\n      type: \"credentials\",\n      provider: provider.id\n    };\n    try {\n      const isAllowed = await callbacks.signIn({\n        user,\n        account,\n        credentials\n      });\n      if (!isAllowed) {\n        return {\n          status: 403,\n          redirect: `${url}/error?error=AccessDenied`,\n          cookies\n        };\n      } else if (typeof isAllowed === \"string\") {\n        return {\n          redirect: isAllowed,\n          cookies\n        };\n      }\n    } catch (error) {\n      return {\n        redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n        cookies\n      };\n    }\n    const defaultToken = {\n      name: user.name,\n      email: user.email,\n      picture: user.image,\n      sub: (_user$id3 = user.id) === null || _user$id3 === void 0 ? void 0 : _user$id3.toString()\n    };\n    const token = await callbacks.jwt({\n      token: defaultToken,\n      user,\n      account,\n      isNewUser: false,\n      trigger: \"signIn\"\n    });\n    const newToken = await jwt.encode({\n      ...jwt,\n      token\n    });\n    const cookieExpires = new Date();\n    cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n    const sessionCookies = sessionStore.chunk(newToken, {\n      expires: cookieExpires\n    });\n    cookies.push(...sessionCookies);\n    await ((_events$signIn3 = events.signIn) === null || _events$signIn3 === void 0 ? void 0 : _events$signIn3.call(events, {\n      user,\n      account\n    }));\n    return {\n      redirect: callbackUrl,\n      cookies\n    };\n  }\n  return {\n    status: 500,\n    body: `Error: Callback for provider type ${provider.type} not supported`,\n    cookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/callback.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/next-auth/core/routes/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"callback\", ({\n  enumerable: true,\n  get: function () {\n    return _callback.default;\n  }\n}));\nObject.defineProperty(exports, \"providers\", ({\n  enumerable: true,\n  get: function () {\n    return _providers.default;\n  }\n}));\nObject.defineProperty(exports, \"session\", ({\n  enumerable: true,\n  get: function () {\n    return _session.default;\n  }\n}));\nObject.defineProperty(exports, \"signin\", ({\n  enumerable: true,\n  get: function () {\n    return _signin.default;\n  }\n}));\nObject.defineProperty(exports, \"signout\", ({\n  enumerable: true,\n  get: function () {\n    return _signout.default;\n  }\n}));\nvar _callback = _interopRequireDefault(__webpack_require__(/*! ./callback */ \"(rsc)/./node_modules/next-auth/core/routes/callback.js\"));\nvar _signin = _interopRequireDefault(__webpack_require__(/*! ./signin */ \"(rsc)/./node_modules/next-auth/core/routes/signin.js\"));\nvar _signout = _interopRequireDefault(__webpack_require__(/*! ./signout */ \"(rsc)/./node_modules/next-auth/core/routes/signout.js\"));\nvar _session = _interopRequireDefault(__webpack_require__(/*! ./session */ \"(rsc)/./node_modules/next-auth/core/routes/session.js\"));\nvar _providers = _interopRequireDefault(__webpack_require__(/*! ./providers */ \"(rsc)/./node_modules/next-auth/core/routes/providers.js\"));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/providers.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/routes/providers.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = providers;\nfunction providers(providers) {\n  return {\n    headers: [{\n      key: \"Content-Type\",\n      value: \"application/json\"\n    }],\n    body: providers.reduce((acc, {\n      id,\n      name,\n      type,\n      signinUrl,\n      callbackUrl\n    }) => {\n      acc[id] = {\n        id,\n        name,\n        type,\n        signinUrl,\n        callbackUrl\n      };\n      return acc;\n    }, {})\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvcm91dGVzL3Byb3ZpZGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLElBQUk7QUFDVDtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtYXV0aC9jb3JlL3JvdXRlcy9wcm92aWRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBwcm92aWRlcnM7XG5mdW5jdGlvbiBwcm92aWRlcnMocHJvdmlkZXJzKSB7XG4gIHJldHVybiB7XG4gICAgaGVhZGVyczogW3tcbiAgICAgIGtleTogXCJDb250ZW50LVR5cGVcIixcbiAgICAgIHZhbHVlOiBcImFwcGxpY2F0aW9uL2pzb25cIlxuICAgIH1dLFxuICAgIGJvZHk6IHByb3ZpZGVycy5yZWR1Y2UoKGFjYywge1xuICAgICAgaWQsXG4gICAgICBuYW1lLFxuICAgICAgdHlwZSxcbiAgICAgIHNpZ25pblVybCxcbiAgICAgIGNhbGxiYWNrVXJsXG4gICAgfSkgPT4ge1xuICAgICAgYWNjW2lkXSA9IHtcbiAgICAgICAgaWQsXG4gICAgICAgIG5hbWUsXG4gICAgICAgIHR5cGUsXG4gICAgICAgIHNpZ25pblVybCxcbiAgICAgICAgY2FsbGJhY2tVcmxcbiAgICAgIH07XG4gICAgICByZXR1cm4gYWNjO1xuICAgIH0sIHt9KVxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/providers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/session.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/core/routes/session.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = session;\nvar _utils = __webpack_require__(/*! ../lib/utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nasync function session(params) {\n  const {\n    options,\n    sessionStore,\n    newSession,\n    isUpdate\n  } = params;\n  const {\n    adapter,\n    jwt,\n    events,\n    callbacks,\n    logger,\n    session: {\n      strategy: sessionStrategy,\n      maxAge: sessionMaxAge\n    }\n  } = options;\n  const response = {\n    body: {},\n    headers: [{\n      key: \"Content-Type\",\n      value: \"application/json\"\n    }],\n    cookies: []\n  };\n  const sessionToken = sessionStore.value;\n  if (!sessionToken) return response;\n  if (sessionStrategy === \"jwt\") {\n    try {\n      var _response$cookies, _events$session;\n      const decodedToken = await jwt.decode({\n        ...jwt,\n        token: sessionToken\n      });\n      if (!decodedToken) throw new Error(\"JWT invalid\");\n      const token = await callbacks.jwt({\n        token: decodedToken,\n        ...(isUpdate && {\n          trigger: \"update\"\n        }),\n        session: newSession\n      });\n      const newExpires = (0, _utils.fromDate)(sessionMaxAge);\n      const updatedSession = await callbacks.session({\n        session: {\n          user: {\n            name: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.name,\n            email: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.email,\n            image: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.picture\n          },\n          expires: newExpires.toISOString()\n        },\n        token\n      });\n      response.body = updatedSession;\n      const newToken = await jwt.encode({\n        ...jwt,\n        token,\n        maxAge: options.session.maxAge\n      });\n      const sessionCookies = sessionStore.chunk(newToken, {\n        expires: newExpires\n      });\n      (_response$cookies = response.cookies) === null || _response$cookies === void 0 || _response$cookies.push(...sessionCookies);\n      await ((_events$session = events.session) === null || _events$session === void 0 ? void 0 : _events$session.call(events, {\n        session: updatedSession,\n        token\n      }));\n    } catch (error) {\n      var _response$cookies2;\n      logger.error(\"JWT_SESSION_ERROR\", error);\n      (_response$cookies2 = response.cookies) === null || _response$cookies2 === void 0 || _response$cookies2.push(...sessionStore.clean());\n    }\n  } else {\n    try {\n      const {\n        getSessionAndUser,\n        deleteSession,\n        updateSession\n      } = adapter;\n      let userAndSession = await getSessionAndUser(sessionToken);\n      if (userAndSession && userAndSession.session.expires.valueOf() < Date.now()) {\n        await deleteSession(sessionToken);\n        userAndSession = null;\n      }\n      if (userAndSession) {\n        var _response$cookies3, _events$session2;\n        const {\n          user,\n          session\n        } = userAndSession;\n        const sessionUpdateAge = options.session.updateAge;\n        const sessionIsDueToBeUpdatedDate = session.expires.valueOf() - sessionMaxAge * 1000 + sessionUpdateAge * 1000;\n        const newExpires = (0, _utils.fromDate)(sessionMaxAge);\n        if (sessionIsDueToBeUpdatedDate <= Date.now()) {\n          await updateSession({\n            sessionToken,\n            expires: newExpires\n          });\n        }\n        const sessionPayload = await callbacks.session({\n          session: {\n            user: {\n              name: user.name,\n              email: user.email,\n              image: user.image\n            },\n            expires: session.expires.toISOString()\n          },\n          user,\n          newSession,\n          ...(isUpdate ? {\n            trigger: \"update\"\n          } : {})\n        });\n        response.body = sessionPayload;\n        (_response$cookies3 = response.cookies) === null || _response$cookies3 === void 0 || _response$cookies3.push({\n          name: options.cookies.sessionToken.name,\n          value: sessionToken,\n          options: {\n            ...options.cookies.sessionToken.options,\n            expires: newExpires\n          }\n        });\n        await ((_events$session2 = events.session) === null || _events$session2 === void 0 ? void 0 : _events$session2.call(events, {\n          session: sessionPayload\n        }));\n      } else if (sessionToken) {\n        var _response$cookies4;\n        (_response$cookies4 = response.cookies) === null || _response$cookies4 === void 0 || _response$cookies4.push(...sessionStore.clean());\n      }\n    } catch (error) {\n      logger.error(\"SESSION_ERROR\", error);\n    }\n  }\n  return response;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/session.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/signin.js":
/*!******************************************************!*\
  !*** ./node_modules/next-auth/core/routes/signin.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = signin;\nvar _authorizationUrl = _interopRequireDefault(__webpack_require__(/*! ../lib/oauth/authorization-url */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/authorization-url.js\"));\nvar _signin = _interopRequireDefault(__webpack_require__(/*! ../lib/email/signin */ \"(rsc)/./node_modules/next-auth/core/lib/email/signin.js\"));\nvar _getUserFromEmail = _interopRequireDefault(__webpack_require__(/*! ../lib/email/getUserFromEmail */ \"(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js\"));\nasync function signin(params) {\n  const {\n    options,\n    query,\n    body\n  } = params;\n  const {\n    url,\n    callbacks,\n    logger,\n    provider\n  } = options;\n  if (!provider.type) {\n    return {\n      status: 500,\n      text: `Error: Type not specified for ${provider.name}`\n    };\n  }\n  if (provider.type === \"oauth\") {\n    try {\n      const response = await (0, _authorizationUrl.default)({\n        options,\n        query\n      });\n      return response;\n    } catch (error) {\n      logger.error(\"SIGNIN_OAUTH_ERROR\", {\n        error: error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=OAuthSignin`\n      };\n    }\n  } else if (provider.type === \"email\") {\n    var _provider$normalizeId;\n    let email = body === null || body === void 0 ? void 0 : body.email;\n    if (!email) return {\n      redirect: `${url}/error?error=EmailSignin`\n    };\n    const normalizer = (_provider$normalizeId = provider.normalizeIdentifier) !== null && _provider$normalizeId !== void 0 ? _provider$normalizeId : identifier => {\n      let [local, domain] = identifier.toLowerCase().trim().split(\"@\");\n      domain = domain.split(\",\")[0];\n      return `${local}@${domain}`;\n    };\n    try {\n      email = normalizer(body === null || body === void 0 ? void 0 : body.email);\n    } catch (error) {\n      logger.error(\"SIGNIN_EMAIL_ERROR\", {\n        error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=EmailSignin`\n      };\n    }\n    const user = await (0, _getUserFromEmail.default)({\n      email,\n      adapter: options.adapter\n    });\n    const account = {\n      providerAccountId: email,\n      userId: email,\n      type: \"email\",\n      provider: provider.id\n    };\n    try {\n      const signInCallbackResponse = await callbacks.signIn({\n        user,\n        account,\n        email: {\n          verificationRequest: true\n        }\n      });\n      if (!signInCallbackResponse) {\n        return {\n          redirect: `${url}/error?error=AccessDenied`\n        };\n      } else if (typeof signInCallbackResponse === \"string\") {\n        return {\n          redirect: signInCallbackResponse\n        };\n      }\n    } catch (error) {\n      return {\n        redirect: `${url}/error?${new URLSearchParams({\n          error: error\n        })}`\n      };\n    }\n    try {\n      const redirect = await (0, _signin.default)(email, options);\n      return {\n        redirect\n      };\n    } catch (error) {\n      logger.error(\"SIGNIN_EMAIL_ERROR\", {\n        error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=EmailSignin`\n      };\n    }\n  }\n  return {\n    redirect: `${url}/signin`\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/signin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/signout.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/core/routes/signout.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = signout;\nasync function signout(params) {\n  const {\n    options,\n    sessionStore\n  } = params;\n  const {\n    adapter,\n    events,\n    jwt,\n    callbackUrl,\n    logger,\n    session\n  } = options;\n  const sessionToken = sessionStore === null || sessionStore === void 0 ? void 0 : sessionStore.value;\n  if (!sessionToken) {\n    return {\n      redirect: callbackUrl\n    };\n  }\n  if (session.strategy === \"jwt\") {\n    try {\n      var _events$signOut;\n      const decodedJwt = await jwt.decode({\n        ...jwt,\n        token: sessionToken\n      });\n      await ((_events$signOut = events.signOut) === null || _events$signOut === void 0 ? void 0 : _events$signOut.call(events, {\n        token: decodedJwt\n      }));\n    } catch (error) {\n      logger.error(\"SIGNOUT_ERROR\", error);\n    }\n  } else {\n    try {\n      var _events$signOut2;\n      const session = await adapter.deleteSession(sessionToken);\n      await ((_events$signOut2 = events.signOut) === null || _events$signOut2 === void 0 ? void 0 : _events$signOut2.call(events, {\n        session\n      }));\n    } catch (error) {\n      logger.error(\"SIGNOUT_ERROR\", error);\n    }\n  }\n  const sessionCookies = sessionStore.clean();\n  return {\n    redirect: callbackUrl,\n    cookies: sessionCookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvcm91dGVzL3NpZ25vdXQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ047QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvcm91dGVzL3NpZ25vdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBzaWdub3V0O1xuYXN5bmMgZnVuY3Rpb24gc2lnbm91dChwYXJhbXMpIHtcbiAgY29uc3Qge1xuICAgIG9wdGlvbnMsXG4gICAgc2Vzc2lvblN0b3JlXG4gIH0gPSBwYXJhbXM7XG4gIGNvbnN0IHtcbiAgICBhZGFwdGVyLFxuICAgIGV2ZW50cyxcbiAgICBqd3QsXG4gICAgY2FsbGJhY2tVcmwsXG4gICAgbG9nZ2VyLFxuICAgIHNlc3Npb25cbiAgfSA9IG9wdGlvbnM7XG4gIGNvbnN0IHNlc3Npb25Ub2tlbiA9IHNlc3Npb25TdG9yZSA9PT0gbnVsbCB8fCBzZXNzaW9uU3RvcmUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNlc3Npb25TdG9yZS52YWx1ZTtcbiAgaWYgKCFzZXNzaW9uVG9rZW4pIHtcbiAgICByZXR1cm4ge1xuICAgICAgcmVkaXJlY3Q6IGNhbGxiYWNrVXJsXG4gICAgfTtcbiAgfVxuICBpZiAoc2Vzc2lvbi5zdHJhdGVneSA9PT0gXCJqd3RcIikge1xuICAgIHRyeSB7XG4gICAgICB2YXIgX2V2ZW50cyRzaWduT3V0O1xuICAgICAgY29uc3QgZGVjb2RlZEp3dCA9IGF3YWl0IGp3dC5kZWNvZGUoe1xuICAgICAgICAuLi5qd3QsXG4gICAgICAgIHRva2VuOiBzZXNzaW9uVG9rZW5cbiAgICAgIH0pO1xuICAgICAgYXdhaXQgKChfZXZlbnRzJHNpZ25PdXQgPSBldmVudHMuc2lnbk91dCkgPT09IG51bGwgfHwgX2V2ZW50cyRzaWduT3V0ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZXZlbnRzJHNpZ25PdXQuY2FsbChldmVudHMsIHtcbiAgICAgICAgdG9rZW46IGRlY29kZWRKd3RcbiAgICAgIH0pKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgbG9nZ2VyLmVycm9yKFwiU0lHTk9VVF9FUlJPUlwiLCBlcnJvcik7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIHRyeSB7XG4gICAgICB2YXIgX2V2ZW50cyRzaWduT3V0MjtcbiAgICAgIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBhZGFwdGVyLmRlbGV0ZVNlc3Npb24oc2Vzc2lvblRva2VuKTtcbiAgICAgIGF3YWl0ICgoX2V2ZW50cyRzaWduT3V0MiA9IGV2ZW50cy5zaWduT3V0KSA9PT0gbnVsbCB8fCBfZXZlbnRzJHNpZ25PdXQyID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZXZlbnRzJHNpZ25PdXQyLmNhbGwoZXZlbnRzLCB7XG4gICAgICAgIHNlc3Npb25cbiAgICAgIH0pKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgbG9nZ2VyLmVycm9yKFwiU0lHTk9VVF9FUlJPUlwiLCBlcnJvcik7XG4gICAgfVxuICB9XG4gIGNvbnN0IHNlc3Npb25Db29raWVzID0gc2Vzc2lvblN0b3JlLmNsZWFuKCk7XG4gIHJldHVybiB7XG4gICAgcmVkaXJlY3Q6IGNhbGxiYWNrVXJsLFxuICAgIGNvb2tpZXM6IHNlc3Npb25Db29raWVzXG4gIH07XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/signout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/types.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/core/types.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtYXV0aC9jb3JlL3R5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/css/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/css/index.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports = function() { return \":root{--border-width:1px;--border-radius:0.5rem;--color-error:#c94b4b;--color-info:#157efb;--color-info-hover:#0f6ddb;--color-info-text:#fff}.__next-auth-theme-auto,.__next-auth-theme-light{--color-background:#ececec;--color-background-hover:hsla(0,0%,93%,.8);--color-background-card:#fff;--color-text:#000;--color-primary:#444;--color-control-border:#bbb;--color-button-active-background:#f9f9f9;--color-button-active-border:#aaa;--color-separator:#ccc}.__next-auth-theme-dark{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}@media (prefers-color-scheme:dark){.__next-auth-theme-auto{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}a.button,button{background-color:var(--provider-dark-bg,var(--color-background));color:var(--provider-dark-color,var(--color-primary))}a.button:hover,button:hover{background-color:var(--provider-dark-bg-hover,var(--color-background-hover))!important}#provider-logo{display:none!important}#provider-logo-dark{display:block!important;width:25px}}html{box-sizing:border-box}*,:after,:before{box-sizing:inherit;margin:0;padding:0}body{background-color:var(--color-background);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;margin:0;padding:0}h1{font-weight:400}h1,p{color:var(--color-text);margin-bottom:1.5rem;padding:0 1rem}form{margin:0;padding:0}label{font-weight:500;margin-bottom:.25rem;text-align:left}input[type],label{color:var(--color-text);display:block}input[type]{background:var(--color-background-card);border:var(--border-width) solid var(--color-control-border);border-radius:var(--border-radius);box-sizing:border-box;font-size:1rem;padding:.5rem 1rem;width:100%}input[type]:focus{box-shadow:none}p{font-size:1.1rem;line-height:2rem}a.button{line-height:1rem;text-decoration:none}a.button:link,a.button:visited{background-color:var(--color-background);color:var(--color-primary)}button span{flex-grow:1}a.button,button{align-items:center;background-color:var(--provider-bg);border-color:rgba(0,0,0,.1);border-radius:var(--border-radius);color:var(--provider-color,var(--color-primary));display:flex;font-size:1.1rem;font-weight:500;justify-content:center;min-height:62px;padding:.75rem 1rem;position:relative;transition:all .1s ease-in-out}a.button:hover,button:hover{background-color:var(--provider-bg-hover,var(--color-background-hover));cursor:pointer}a.button:active,button:active{cursor:pointer}a.button #provider-logo,button #provider-logo{display:block;width:25px}a.button #provider-logo-dark,button #provider-logo-dark{display:none}#submitButton{background-color:var(--brand-color,var(--color-info));color:var(--button-text-color,var(--color-info-text));width:100%}#submitButton:hover{background-color:var(--button-hover-bg,var(--color-info-hover))!important}a.site{color:var(--color-primary);font-size:1rem;line-height:2rem;text-decoration:none}a.site:hover{text-decoration:underline}.page{box-sizing:border-box;display:grid;height:100%;margin:0;padding:0;place-items:center;position:absolute;width:100%}.page>div{text-align:center}.error a.button{margin-top:.5rem;padding-left:2rem;padding-right:2rem}.error .message{margin-bottom:1.5rem}.signin input[type=text]{display:block;margin-left:auto;margin-right:auto}.signin hr{border:0;border-top:1px solid var(--color-separator);display:block;margin:2rem auto 1rem;overflow:visible}.signin hr:before{background:var(--color-background-card);color:#888;content:\\\"or\\\";padding:0 .4rem;position:relative;top:-.7rem}.signin .error{background:#f5f5f5;background:var(--color-error);border-radius:.3rem;font-weight:500}.signin .error p{color:var(--color-info-text);font-size:.9rem;line-height:1.2rem;padding:.5rem 1rem;text-align:left}.signin form,.signin>div{display:block}.signin form input[type],.signin>div input[type]{margin-bottom:.5rem}.signin form button,.signin>div button{width:100%}.signin .provider+.provider{margin-top:1rem}.logo{display:inline-block;margin:1.25rem 0;max-height:70px;max-width:150px}.card{background-color:var(--color-background-card);border-radius:2rem;padding:1.25rem 2rem}.card .header{color:var(--color-primary)}.section-header{color:var(--color-text)}@media screen and (min-width:450px){.card{margin:2rem 0;width:368px}}@media screen and (max-width:450px){.card{margin:1rem 0;width:343px}}\" }//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/css/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {};\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return _next.default;\n  }\n}));\nvar _types = __webpack_require__(/*! ./core/types */ \"(rsc)/./node_modules/next-auth/core/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nvar _next = _interopRequireWildcard(__webpack_require__(/*! ./next */ \"(rsc)/./node_modules/next-auth/next/index.js\"));\nObject.keys(_next).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _next[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _next[key];\n    }\n  });\n});\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/jwt/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/jwt/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {\n  encode: true,\n  decode: true,\n  getToken: true\n};\nexports.decode = decode;\nexports.encode = encode;\nexports.getToken = getToken;\nvar _jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nvar _hkdf = _interopRequireDefault(__webpack_require__(/*! @panva/hkdf */ \"(rsc)/./node_modules/@panva/hkdf/dist/node/cjs/index.js\"));\nvar _uuid = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/index.js\");\nvar _cookie = __webpack_require__(/*! ../core/lib/cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\");\nvar _types = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/next-auth/jwt/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nconst DEFAULT_MAX_AGE = 30 * 24 * 60 * 60;\nconst now = () => Date.now() / 1000 | 0;\nasync function encode(params) {\n  const {\n    token = {},\n    secret,\n    maxAge = DEFAULT_MAX_AGE,\n    salt = \"\"\n  } = params;\n  const encryptionSecret = await getDerivedEncryptionKey(secret, salt);\n  return await new _jose.EncryptJWT(token).setProtectedHeader({\n    alg: \"dir\",\n    enc: \"A256GCM\"\n  }).setIssuedAt().setExpirationTime(now() + maxAge).setJti((0, _uuid.v4)()).encrypt(encryptionSecret);\n}\nasync function decode(params) {\n  const {\n    token,\n    secret,\n    salt = \"\"\n  } = params;\n  if (!token) return null;\n  const encryptionSecret = await getDerivedEncryptionKey(secret, salt);\n  const {\n    payload\n  } = await (0, _jose.jwtDecrypt)(token, encryptionSecret, {\n    clockTolerance: 15\n  });\n  return payload;\n}\nasync function getToken(params) {\n  var _process$env$NEXTAUTH, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _req$headers;\n  const {\n    req,\n    secureCookie = (_process$env$NEXTAUTH = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL) === null || _process$env$NEXTAUTH2 === void 0 ? void 0 : _process$env$NEXTAUTH2.startsWith(\"https://\")) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : !!process.env.VERCEL,\n    cookieName = secureCookie ? \"__Secure-next-auth.session-token\" : \"next-auth.session-token\",\n    raw,\n    decode: _decode = decode,\n    logger = console,\n    secret = (_process$env$NEXTAUTH3 = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.AUTH_SECRET\n  } = params;\n  if (!req) throw new Error(\"Must pass `req` to JWT getToken()\");\n  const sessionStore = new _cookie.SessionStore({\n    name: cookieName,\n    options: {\n      secure: secureCookie\n    }\n  }, {\n    cookies: req.cookies,\n    headers: req.headers\n  }, logger);\n  let token = sessionStore.value;\n  const authorizationHeader = req.headers instanceof Headers ? req.headers.get(\"authorization\") : (_req$headers = req.headers) === null || _req$headers === void 0 ? void 0 : _req$headers.authorization;\n  if (!token && (authorizationHeader === null || authorizationHeader === void 0 ? void 0 : authorizationHeader.split(\" \")[0]) === \"Bearer\") {\n    const urlEncodedToken = authorizationHeader.split(\" \")[1];\n    token = decodeURIComponent(urlEncodedToken);\n  }\n  if (!token) return null;\n  if (raw) return token;\n  try {\n    return await _decode({\n      token,\n      secret\n    });\n  } catch (_unused) {\n    return null;\n  }\n}\nasync function getDerivedEncryptionKey(keyMaterial, salt) {\n  return await (0, _hkdf.default)(\"sha256\", keyMaterial, salt, `NextAuth.js Generated Encryption Key${salt ? ` (${salt})` : \"\"}`, 32);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/jwt/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/jwt/types.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/jwt/types.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2p3dC90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2p3dC90eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/jwt/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/next/index.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/next/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.getServerSession = getServerSession;\nexports.unstable_getServerSession = unstable_getServerSession;\nvar _core = __webpack_require__(/*! ../core */ \"(rsc)/./node_modules/next-auth/core/index.js\");\nvar _utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/next-auth/next/utils.js\");\nasync function NextAuthApiHandler(req, res, options) {\n  var _options$secret, _ref, _options$jwt$secret, _options$jwt, _ref2, _handler$status, _handler$cookies, _handler$headers;\n  const {\n    nextauth,\n    ...query\n  } = req.query;\n  (_options$secret = options.secret) !== null && _options$secret !== void 0 ? _options$secret : options.secret = (_ref = (_options$jwt$secret = (_options$jwt = options.jwt) === null || _options$jwt === void 0 ? void 0 : _options$jwt.secret) !== null && _options$jwt$secret !== void 0 ? _options$jwt$secret : process.env.NEXTAUTH_SECRET) !== null && _ref !== void 0 ? _ref : process.env.AUTH_SECRET;\n  const handler = await (0, _core.AuthHandler)({\n    req: {\n      body: req.body,\n      query,\n      cookies: req.cookies,\n      headers: req.headers,\n      method: req.method,\n      action: nextauth === null || nextauth === void 0 ? void 0 : nextauth[0],\n      providerId: nextauth === null || nextauth === void 0 ? void 0 : nextauth[1],\n      error: (_ref2 = req.query.error) !== null && _ref2 !== void 0 ? _ref2 : nextauth === null || nextauth === void 0 ? void 0 : nextauth[1]\n    },\n    options\n  });\n  res.status((_handler$status = handler.status) !== null && _handler$status !== void 0 ? _handler$status : 200);\n  (_handler$cookies = handler.cookies) === null || _handler$cookies === void 0 || _handler$cookies.forEach(cookie => (0, _utils.setCookie)(res, cookie));\n  (_handler$headers = handler.headers) === null || _handler$headers === void 0 || _handler$headers.forEach(h => res.setHeader(h.key, h.value));\n  if (handler.redirect) {\n    var _req$body;\n    if (((_req$body = req.body) === null || _req$body === void 0 ? void 0 : _req$body.json) !== \"true\") {\n      res.status(302).setHeader(\"Location\", handler.redirect);\n      res.end();\n      return;\n    }\n    return res.json({\n      url: handler.redirect\n    });\n  }\n  return res.send(handler.body);\n}\nasync function NextAuthRouteHandler(req, context, options) {\n  var _options$secret2, _process$env$NEXTAUTH, _await$context$params, _query$error;\n  (_options$secret2 = options.secret) !== null && _options$secret2 !== void 0 ? _options$secret2 : options.secret = (_process$env$NEXTAUTH = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.AUTH_SECRET;\n  const {\n    headers,\n    cookies\n  } = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n  const nextauth = (_await$context$params = await context.params) === null || _await$context$params === void 0 ? void 0 : _await$context$params.nextauth;\n  const query = Object.fromEntries(req.nextUrl.searchParams);\n  const body = await (0, _utils.getBody)(req);\n  const internalResponse = await (0, _core.AuthHandler)({\n    req: {\n      body,\n      query,\n      cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value])),\n      headers: Object.fromEntries(await headers()),\n      method: req.method,\n      action: nextauth === null || nextauth === void 0 ? void 0 : nextauth[0],\n      providerId: nextauth === null || nextauth === void 0 ? void 0 : nextauth[1],\n      error: (_query$error = query.error) !== null && _query$error !== void 0 ? _query$error : nextauth === null || nextauth === void 0 ? void 0 : nextauth[1]\n    },\n    options\n  });\n  const response = (0, _utils.toResponse)(internalResponse);\n  const redirect = response.headers.get(\"Location\");\n  if ((body === null || body === void 0 ? void 0 : body.json) === \"true\" && redirect) {\n    response.headers.delete(\"Location\");\n    response.headers.set(\"Content-Type\", \"application/json\");\n    return new Response(JSON.stringify({\n      url: redirect\n    }), {\n      status: internalResponse.status,\n      headers: response.headers\n    });\n  }\n  return response;\n}\nfunction NextAuth(...args) {\n  var _args$;\n  if (args.length === 1) {\n    return async (req, res) => {\n      if (res !== null && res !== void 0 && res.params) {\n        return await NextAuthRouteHandler(req, res, args[0]);\n      }\n      return await NextAuthApiHandler(req, res, args[0]);\n    };\n  }\n  if ((_args$ = args[1]) !== null && _args$ !== void 0 && _args$.params) {\n    return NextAuthRouteHandler(...args);\n  }\n  return NextAuthApiHandler(...args);\n}\nvar _default = exports[\"default\"] = NextAuth;\nasync function getServerSession(...args) {\n  var _options, _options$secret3, _process$env$NEXTAUTH2;\n  const isRSC = args.length === 0 || args.length === 1;\n  let req, res, options;\n  if (isRSC) {\n    options = Object.assign({}, args[0], {\n      providers: []\n    });\n    const {\n      headers,\n      cookies\n    } = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n    req = {\n      headers: Object.fromEntries(await headers()),\n      cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value]))\n    };\n    res = {\n      getHeader() {},\n      setCookie() {},\n      setHeader() {}\n    };\n  } else {\n    req = args[0];\n    res = args[1];\n    options = Object.assign({}, args[2], {\n      providers: []\n    });\n  }\n  (_options$secret3 = (_options = options).secret) !== null && _options$secret3 !== void 0 ? _options$secret3 : _options.secret = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.AUTH_SECRET;\n  const session = await (0, _core.AuthHandler)({\n    options,\n    req: {\n      action: \"session\",\n      method: \"GET\",\n      cookies: req.cookies,\n      headers: req.headers\n    }\n  });\n  const {\n    body,\n    cookies,\n    status = 200\n  } = session;\n  cookies === null || cookies === void 0 || cookies.forEach(cookie => (0, _utils.setCookie)(res, cookie));\n  if (body && typeof body !== \"string\" && Object.keys(body).length) {\n    if (status === 200) {\n      if (isRSC) delete body.expires;\n      return body;\n    }\n    throw new Error(body.message);\n  }\n  return null;\n}\nlet deprecatedWarningShown = false;\nasync function unstable_getServerSession(...args) {\n  if (!deprecatedWarningShown && \"development\" !== \"production\") {\n    console.warn(\"`unstable_getServerSession` has been renamed to `getServerSession`.\");\n    deprecatedWarningShown = true;\n  }\n  return await getServerSession(...args);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25leHQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZix3QkFBd0I7QUFDeEIsaUNBQWlDO0FBQ2pDLFlBQVksbUJBQU8sQ0FBQyw2REFBUztBQUM3QixhQUFhLG1CQUFPLENBQUMsNkRBQVM7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLEVBQUUsbUJBQU8sQ0FBQyxtRUFBYztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0JBQWU7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxNQUFNLEVBQUUsbUJBQU8sQ0FBQyxtRUFBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGFBQW9CO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25leHQvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG5leHBvcnRzLmdldFNlcnZlclNlc3Npb24gPSBnZXRTZXJ2ZXJTZXNzaW9uO1xuZXhwb3J0cy51bnN0YWJsZV9nZXRTZXJ2ZXJTZXNzaW9uID0gdW5zdGFibGVfZ2V0U2VydmVyU2Vzc2lvbjtcbnZhciBfY29yZSA9IHJlcXVpcmUoXCIuLi9jb3JlXCIpO1xudmFyIF91dGlscyA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xuYXN5bmMgZnVuY3Rpb24gTmV4dEF1dGhBcGlIYW5kbGVyKHJlcSwgcmVzLCBvcHRpb25zKSB7XG4gIHZhciBfb3B0aW9ucyRzZWNyZXQsIF9yZWYsIF9vcHRpb25zJGp3dCRzZWNyZXQsIF9vcHRpb25zJGp3dCwgX3JlZjIsIF9oYW5kbGVyJHN0YXR1cywgX2hhbmRsZXIkY29va2llcywgX2hhbmRsZXIkaGVhZGVycztcbiAgY29uc3Qge1xuICAgIG5leHRhdXRoLFxuICAgIC4uLnF1ZXJ5XG4gIH0gPSByZXEucXVlcnk7XG4gIChfb3B0aW9ucyRzZWNyZXQgPSBvcHRpb25zLnNlY3JldCkgIT09IG51bGwgJiYgX29wdGlvbnMkc2VjcmV0ICE9PSB2b2lkIDAgPyBfb3B0aW9ucyRzZWNyZXQgOiBvcHRpb25zLnNlY3JldCA9IChfcmVmID0gKF9vcHRpb25zJGp3dCRzZWNyZXQgPSAoX29wdGlvbnMkand0ID0gb3B0aW9ucy5qd3QpID09PSBudWxsIHx8IF9vcHRpb25zJGp3dCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX29wdGlvbnMkand0LnNlY3JldCkgIT09IG51bGwgJiYgX29wdGlvbnMkand0JHNlY3JldCAhPT0gdm9pZCAwID8gX29wdGlvbnMkand0JHNlY3JldCA6IHByb2Nlc3MuZW52Lk5FWFRBVVRIX1NFQ1JFVCkgIT09IG51bGwgJiYgX3JlZiAhPT0gdm9pZCAwID8gX3JlZiA6IHByb2Nlc3MuZW52LkFVVEhfU0VDUkVUO1xuICBjb25zdCBoYW5kbGVyID0gYXdhaXQgKDAsIF9jb3JlLkF1dGhIYW5kbGVyKSh7XG4gICAgcmVxOiB7XG4gICAgICBib2R5OiByZXEuYm9keSxcbiAgICAgIHF1ZXJ5LFxuICAgICAgY29va2llczogcmVxLmNvb2tpZXMsXG4gICAgICBoZWFkZXJzOiByZXEuaGVhZGVycyxcbiAgICAgIG1ldGhvZDogcmVxLm1ldGhvZCxcbiAgICAgIGFjdGlvbjogbmV4dGF1dGggPT09IG51bGwgfHwgbmV4dGF1dGggPT09IHZvaWQgMCA/IHZvaWQgMCA6IG5leHRhdXRoWzBdLFxuICAgICAgcHJvdmlkZXJJZDogbmV4dGF1dGggPT09IG51bGwgfHwgbmV4dGF1dGggPT09IHZvaWQgMCA/IHZvaWQgMCA6IG5leHRhdXRoWzFdLFxuICAgICAgZXJyb3I6IChfcmVmMiA9IHJlcS5xdWVyeS5lcnJvcikgIT09IG51bGwgJiYgX3JlZjIgIT09IHZvaWQgMCA/IF9yZWYyIDogbmV4dGF1dGggPT09IG51bGwgfHwgbmV4dGF1dGggPT09IHZvaWQgMCA/IHZvaWQgMCA6IG5leHRhdXRoWzFdXG4gICAgfSxcbiAgICBvcHRpb25zXG4gIH0pO1xuICByZXMuc3RhdHVzKChfaGFuZGxlciRzdGF0dXMgPSBoYW5kbGVyLnN0YXR1cykgIT09IG51bGwgJiYgX2hhbmRsZXIkc3RhdHVzICE9PSB2b2lkIDAgPyBfaGFuZGxlciRzdGF0dXMgOiAyMDApO1xuICAoX2hhbmRsZXIkY29va2llcyA9IGhhbmRsZXIuY29va2llcykgPT09IG51bGwgfHwgX2hhbmRsZXIkY29va2llcyA9PT0gdm9pZCAwIHx8IF9oYW5kbGVyJGNvb2tpZXMuZm9yRWFjaChjb29raWUgPT4gKDAsIF91dGlscy5zZXRDb29raWUpKHJlcywgY29va2llKSk7XG4gIChfaGFuZGxlciRoZWFkZXJzID0gaGFuZGxlci5oZWFkZXJzKSA9PT0gbnVsbCB8fCBfaGFuZGxlciRoZWFkZXJzID09PSB2b2lkIDAgfHwgX2hhbmRsZXIkaGVhZGVycy5mb3JFYWNoKGggPT4gcmVzLnNldEhlYWRlcihoLmtleSwgaC52YWx1ZSkpO1xuICBpZiAoaGFuZGxlci5yZWRpcmVjdCkge1xuICAgIHZhciBfcmVxJGJvZHk7XG4gICAgaWYgKCgoX3JlcSRib2R5ID0gcmVxLmJvZHkpID09PSBudWxsIHx8IF9yZXEkYm9keSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3JlcSRib2R5Lmpzb24pICE9PSBcInRydWVcIikge1xuICAgICAgcmVzLnN0YXR1cygzMDIpLnNldEhlYWRlcihcIkxvY2F0aW9uXCIsIGhhbmRsZXIucmVkaXJlY3QpO1xuICAgICAgcmVzLmVuZCgpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICByZXR1cm4gcmVzLmpzb24oe1xuICAgICAgdXJsOiBoYW5kbGVyLnJlZGlyZWN0XG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIHJlcy5zZW5kKGhhbmRsZXIuYm9keSk7XG59XG5hc3luYyBmdW5jdGlvbiBOZXh0QXV0aFJvdXRlSGFuZGxlcihyZXEsIGNvbnRleHQsIG9wdGlvbnMpIHtcbiAgdmFyIF9vcHRpb25zJHNlY3JldDIsIF9wcm9jZXNzJGVudiRORVhUQVVUSCwgX2F3YWl0JGNvbnRleHQkcGFyYW1zLCBfcXVlcnkkZXJyb3I7XG4gIChfb3B0aW9ucyRzZWNyZXQyID0gb3B0aW9ucy5zZWNyZXQpICE9PSBudWxsICYmIF9vcHRpb25zJHNlY3JldDIgIT09IHZvaWQgMCA/IF9vcHRpb25zJHNlY3JldDIgOiBvcHRpb25zLnNlY3JldCA9IChfcHJvY2VzcyRlbnYkTkVYVEFVVEggPSBwcm9jZXNzLmVudi5ORVhUQVVUSF9TRUNSRVQpICE9PSBudWxsICYmIF9wcm9jZXNzJGVudiRORVhUQVVUSCAhPT0gdm9pZCAwID8gX3Byb2Nlc3MkZW52JE5FWFRBVVRIIDogcHJvY2Vzcy5lbnYuQVVUSF9TRUNSRVQ7XG4gIGNvbnN0IHtcbiAgICBoZWFkZXJzLFxuICAgIGNvb2tpZXNcbiAgfSA9IHJlcXVpcmUoXCJuZXh0L2hlYWRlcnNcIik7XG4gIGNvbnN0IG5leHRhdXRoID0gKF9hd2FpdCRjb250ZXh0JHBhcmFtcyA9IGF3YWl0IGNvbnRleHQucGFyYW1zKSA9PT0gbnVsbCB8fCBfYXdhaXQkY29udGV4dCRwYXJhbXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hd2FpdCRjb250ZXh0JHBhcmFtcy5uZXh0YXV0aDtcbiAgY29uc3QgcXVlcnkgPSBPYmplY3QuZnJvbUVudHJpZXMocmVxLm5leHRVcmwuc2VhcmNoUGFyYW1zKTtcbiAgY29uc3QgYm9keSA9IGF3YWl0ICgwLCBfdXRpbHMuZ2V0Qm9keSkocmVxKTtcbiAgY29uc3QgaW50ZXJuYWxSZXNwb25zZSA9IGF3YWl0ICgwLCBfY29yZS5BdXRoSGFuZGxlcikoe1xuICAgIHJlcToge1xuICAgICAgYm9keSxcbiAgICAgIHF1ZXJ5LFxuICAgICAgY29va2llczogT2JqZWN0LmZyb21FbnRyaWVzKChhd2FpdCBjb29raWVzKCkpLmdldEFsbCgpLm1hcChjID0+IFtjLm5hbWUsIGMudmFsdWVdKSksXG4gICAgICBoZWFkZXJzOiBPYmplY3QuZnJvbUVudHJpZXMoYXdhaXQgaGVhZGVycygpKSxcbiAgICAgIG1ldGhvZDogcmVxLm1ldGhvZCxcbiAgICAgIGFjdGlvbjogbmV4dGF1dGggPT09IG51bGwgfHwgbmV4dGF1dGggPT09IHZvaWQgMCA/IHZvaWQgMCA6IG5leHRhdXRoWzBdLFxuICAgICAgcHJvdmlkZXJJZDogbmV4dGF1dGggPT09IG51bGwgfHwgbmV4dGF1dGggPT09IHZvaWQgMCA/IHZvaWQgMCA6IG5leHRhdXRoWzFdLFxuICAgICAgZXJyb3I6IChfcXVlcnkkZXJyb3IgPSBxdWVyeS5lcnJvcikgIT09IG51bGwgJiYgX3F1ZXJ5JGVycm9yICE9PSB2b2lkIDAgPyBfcXVlcnkkZXJyb3IgOiBuZXh0YXV0aCA9PT0gbnVsbCB8fCBuZXh0YXV0aCA9PT0gdm9pZCAwID8gdm9pZCAwIDogbmV4dGF1dGhbMV1cbiAgICB9LFxuICAgIG9wdGlvbnNcbiAgfSk7XG4gIGNvbnN0IHJlc3BvbnNlID0gKDAsIF91dGlscy50b1Jlc3BvbnNlKShpbnRlcm5hbFJlc3BvbnNlKTtcbiAgY29uc3QgcmVkaXJlY3QgPSByZXNwb25zZS5oZWFkZXJzLmdldChcIkxvY2F0aW9uXCIpO1xuICBpZiAoKGJvZHkgPT09IG51bGwgfHwgYm9keSA9PT0gdm9pZCAwID8gdm9pZCAwIDogYm9keS5qc29uKSA9PT0gXCJ0cnVlXCIgJiYgcmVkaXJlY3QpIHtcbiAgICByZXNwb25zZS5oZWFkZXJzLmRlbGV0ZShcIkxvY2F0aW9uXCIpO1xuICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KFwiQ29udGVudC1UeXBlXCIsIFwiYXBwbGljYXRpb24vanNvblwiKTtcbiAgICByZXR1cm4gbmV3IFJlc3BvbnNlKEpTT04uc3RyaW5naWZ5KHtcbiAgICAgIHVybDogcmVkaXJlY3RcbiAgICB9KSwge1xuICAgICAgc3RhdHVzOiBpbnRlcm5hbFJlc3BvbnNlLnN0YXR1cyxcbiAgICAgIGhlYWRlcnM6IHJlc3BvbnNlLmhlYWRlcnNcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gcmVzcG9uc2U7XG59XG5mdW5jdGlvbiBOZXh0QXV0aCguLi5hcmdzKSB7XG4gIHZhciBfYXJncyQ7XG4gIGlmIChhcmdzLmxlbmd0aCA9PT0gMSkge1xuICAgIHJldHVybiBhc3luYyAocmVxLCByZXMpID0+IHtcbiAgICAgIGlmIChyZXMgIT09IG51bGwgJiYgcmVzICE9PSB2b2lkIDAgJiYgcmVzLnBhcmFtcykge1xuICAgICAgICByZXR1cm4gYXdhaXQgTmV4dEF1dGhSb3V0ZUhhbmRsZXIocmVxLCByZXMsIGFyZ3NbMF0pO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGF3YWl0IE5leHRBdXRoQXBpSGFuZGxlcihyZXEsIHJlcywgYXJnc1swXSk7XG4gICAgfTtcbiAgfVxuICBpZiAoKF9hcmdzJCA9IGFyZ3NbMV0pICE9PSBudWxsICYmIF9hcmdzJCAhPT0gdm9pZCAwICYmIF9hcmdzJC5wYXJhbXMpIHtcbiAgICByZXR1cm4gTmV4dEF1dGhSb3V0ZUhhbmRsZXIoLi4uYXJncyk7XG4gIH1cbiAgcmV0dXJuIE5leHRBdXRoQXBpSGFuZGxlciguLi5hcmdzKTtcbn1cbnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IE5leHRBdXRoO1xuYXN5bmMgZnVuY3Rpb24gZ2V0U2VydmVyU2Vzc2lvbiguLi5hcmdzKSB7XG4gIHZhciBfb3B0aW9ucywgX29wdGlvbnMkc2VjcmV0MywgX3Byb2Nlc3MkZW52JE5FWFRBVVRIMjtcbiAgY29uc3QgaXNSU0MgPSBhcmdzLmxlbmd0aCA9PT0gMCB8fCBhcmdzLmxlbmd0aCA9PT0gMTtcbiAgbGV0IHJlcSwgcmVzLCBvcHRpb25zO1xuICBpZiAoaXNSU0MpIHtcbiAgICBvcHRpb25zID0gT2JqZWN0LmFzc2lnbih7fSwgYXJnc1swXSwge1xuICAgICAgcHJvdmlkZXJzOiBbXVxuICAgIH0pO1xuICAgIGNvbnN0IHtcbiAgICAgIGhlYWRlcnMsXG4gICAgICBjb29raWVzXG4gICAgfSA9IHJlcXVpcmUoXCJuZXh0L2hlYWRlcnNcIik7XG4gICAgcmVxID0ge1xuICAgICAgaGVhZGVyczogT2JqZWN0LmZyb21FbnRyaWVzKGF3YWl0IGhlYWRlcnMoKSksXG4gICAgICBjb29raWVzOiBPYmplY3QuZnJvbUVudHJpZXMoKGF3YWl0IGNvb2tpZXMoKSkuZ2V0QWxsKCkubWFwKGMgPT4gW2MubmFtZSwgYy52YWx1ZV0pKVxuICAgIH07XG4gICAgcmVzID0ge1xuICAgICAgZ2V0SGVhZGVyKCkge30sXG4gICAgICBzZXRDb29raWUoKSB7fSxcbiAgICAgIHNldEhlYWRlcigpIHt9XG4gICAgfTtcbiAgfSBlbHNlIHtcbiAgICByZXEgPSBhcmdzWzBdO1xuICAgIHJlcyA9IGFyZ3NbMV07XG4gICAgb3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oe30sIGFyZ3NbMl0sIHtcbiAgICAgIHByb3ZpZGVyczogW11cbiAgICB9KTtcbiAgfVxuICAoX29wdGlvbnMkc2VjcmV0MyA9IChfb3B0aW9ucyA9IG9wdGlvbnMpLnNlY3JldCkgIT09IG51bGwgJiYgX29wdGlvbnMkc2VjcmV0MyAhPT0gdm9pZCAwID8gX29wdGlvbnMkc2VjcmV0MyA6IF9vcHRpb25zLnNlY3JldCA9IChfcHJvY2VzcyRlbnYkTkVYVEFVVEgyID0gcHJvY2Vzcy5lbnYuTkVYVEFVVEhfU0VDUkVUKSAhPT0gbnVsbCAmJiBfcHJvY2VzcyRlbnYkTkVYVEFVVEgyICE9PSB2b2lkIDAgPyBfcHJvY2VzcyRlbnYkTkVYVEFVVEgyIDogcHJvY2Vzcy5lbnYuQVVUSF9TRUNSRVQ7XG4gIGNvbnN0IHNlc3Npb24gPSBhd2FpdCAoMCwgX2NvcmUuQXV0aEhhbmRsZXIpKHtcbiAgICBvcHRpb25zLFxuICAgIHJlcToge1xuICAgICAgYWN0aW9uOiBcInNlc3Npb25cIixcbiAgICAgIG1ldGhvZDogXCJHRVRcIixcbiAgICAgIGNvb2tpZXM6IHJlcS5jb29raWVzLFxuICAgICAgaGVhZGVyczogcmVxLmhlYWRlcnNcbiAgICB9XG4gIH0pO1xuICBjb25zdCB7XG4gICAgYm9keSxcbiAgICBjb29raWVzLFxuICAgIHN0YXR1cyA9IDIwMFxuICB9ID0gc2Vzc2lvbjtcbiAgY29va2llcyA9PT0gbnVsbCB8fCBjb29raWVzID09PSB2b2lkIDAgfHwgY29va2llcy5mb3JFYWNoKGNvb2tpZSA9PiAoMCwgX3V0aWxzLnNldENvb2tpZSkocmVzLCBjb29raWUpKTtcbiAgaWYgKGJvZHkgJiYgdHlwZW9mIGJvZHkgIT09IFwic3RyaW5nXCIgJiYgT2JqZWN0LmtleXMoYm9keSkubGVuZ3RoKSB7XG4gICAgaWYgKHN0YXR1cyA9PT0gMjAwKSB7XG4gICAgICBpZiAoaXNSU0MpIGRlbGV0ZSBib2R5LmV4cGlyZXM7XG4gICAgICByZXR1cm4gYm9keTtcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKGJvZHkubWVzc2FnZSk7XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59XG5sZXQgZGVwcmVjYXRlZFdhcm5pbmdTaG93biA9IGZhbHNlO1xuYXN5bmMgZnVuY3Rpb24gdW5zdGFibGVfZ2V0U2VydmVyU2Vzc2lvbiguLi5hcmdzKSB7XG4gIGlmICghZGVwcmVjYXRlZFdhcm5pbmdTaG93biAmJiBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIHtcbiAgICBjb25zb2xlLndhcm4oXCJgdW5zdGFibGVfZ2V0U2VydmVyU2Vzc2lvbmAgaGFzIGJlZW4gcmVuYW1lZCB0byBgZ2V0U2VydmVyU2Vzc2lvbmAuXCIpO1xuICAgIGRlcHJlY2F0ZWRXYXJuaW5nU2hvd24gPSB0cnVlO1xuICB9XG4gIHJldHVybiBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKC4uLmFyZ3MpO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/next/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/next/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/next/utils.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.getBody = getBody;\nexports.setCookie = setCookie;\nexports.toResponse = toResponse;\nvar _cookie = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/cookie/index.js\");\nfunction setCookie(res, cookie) {\n  var _res$getHeader;\n  let setCookieHeader = (_res$getHeader = res.getHeader(\"Set-Cookie\")) !== null && _res$getHeader !== void 0 ? _res$getHeader : [];\n  if (!Array.isArray(setCookieHeader)) {\n    setCookieHeader = [setCookieHeader];\n  }\n  const {\n    name,\n    value,\n    options\n  } = cookie;\n  const cookieHeader = (0, _cookie.serialize)(name, value, options);\n  setCookieHeader.push(cookieHeader);\n  res.setHeader(\"Set-Cookie\", setCookieHeader);\n}\nasync function getBody(req) {\n  if (!(\"body\" in req) || !req.body || req.method !== \"POST\") return;\n  const contentType = req.headers.get(\"content-type\");\n  if (contentType !== null && contentType !== void 0 && contentType.includes(\"application/json\")) {\n    return await req.json();\n  } else if (contentType !== null && contentType !== void 0 && contentType.includes(\"application/x-www-form-urlencoded\")) {\n    const params = new URLSearchParams(await req.text());\n    return Object.fromEntries(params);\n  }\n}\nfunction toResponse(res) {\n  var _res$headers, _res$cookies, _res$status;\n  const headers = new Headers((_res$headers = res.headers) === null || _res$headers === void 0 ? void 0 : _res$headers.reduce((acc, {\n    key,\n    value\n  }) => {\n    acc[key] = value;\n    return acc;\n  }, {}));\n  (_res$cookies = res.cookies) === null || _res$cookies === void 0 || _res$cookies.forEach(cookie => {\n    const {\n      name,\n      value,\n      options\n    } = cookie;\n    const cookieHeader = (0, _cookie.serialize)(name, value, options);\n    if (headers.has(\"Set-Cookie\")) headers.append(\"Set-Cookie\", cookieHeader);else headers.set(\"Set-Cookie\", cookieHeader);\n  });\n  let body = res.body;\n  if (headers.get(\"content-type\") === \"application/json\") body = JSON.stringify(res.body);else if (headers.get(\"content-type\") === \"application/x-www-form-urlencoded\") body = new URLSearchParams(res.body).toString();\n  const status = res.redirect ? 302 : (_res$status = res.status) !== null && _res$status !== void 0 ? _res$status : 200;\n  const response = new Response(body, {\n    headers,\n    status\n  });\n  if (res.redirect) response.headers.set(\"Location\", res.redirect);\n  return response;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/next/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/preact-render-to-string/dist/commonjs.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/preact-render-to-string/dist/commonjs.js ***!
  \**************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("!function(e,t){ true?t(exports,__webpack_require__(/*! preact */ \"(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js\")):0}(this,function(e,t){var n=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i,r=/^(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/,o=/[\\s\\n\\\\/='\"\\0<>]/,i=/^xlink:?./,s=/[\"&<]/;function a(e){if(!1===s.test(e+=\"\"))return e;for(var t=0,n=0,r=\"\",o=\"\";n<e.length;n++){switch(e.charCodeAt(n)){case 34:o=\"&quot;\";break;case 38:o=\"&amp;\";break;case 60:o=\"&lt;\";break;default:continue}n!==t&&(r+=e.slice(t,n)),r+=o,t=n+1}return n!==t&&(r+=e.slice(t,n)),r}var l=function(e,t){return String(e).replace(/(\\n+)/g,\"$1\"+(t||\"\\t\"))},f=function(e,t,n){return String(e).length>(t||40)||!n&&-1!==String(e).indexOf(\"\\n\")||-1!==String(e).indexOf(\"<\")},u={},p=/([A-Z])/g;function c(e){var t=\"\";for(var r in e){var o=e[r];null!=o&&\"\"!==o&&(t&&(t+=\" \"),t+=\"-\"==r[0]?r:u[r]||(u[r]=r.replace(p,\"-$1\").toLowerCase()),t=\"number\"==typeof o&&!1===n.test(r)?t+\": \"+o+\"px;\":t+\": \"+o+\";\")}return t||void 0}function _(e,t){return Array.isArray(t)?t.reduce(_,e):null!=t&&!1!==t&&e.push(t),e}function d(){this.__d=!0}function v(e,t){return{__v:e,context:t,props:e.props,setState:d,forceUpdate:d,__d:!0,__h:[]}}function g(e,t){var n=e.contextType,r=n&&t[n.__c];return null!=n?r?r.props.value:n.__:t}var h=[];function y(e,n,s,u,p,d){if(null==e||\"boolean\"==typeof e)return\"\";if(\"object\"!=typeof e)return\"function\"==typeof e?\"\":a(e);var m=s.pretty,b=m&&\"string\"==typeof m?m:\"\\t\";if(Array.isArray(e)){for(var x=\"\",k=0;k<e.length;k++)m&&k>0&&(x+=\"\\n\"),x+=y(e[k],n,s,u,p,d);return x}if(void 0!==e.constructor)return\"\";var S,w=e.type,C=e.props,O=!1;if(\"function\"==typeof w){if(O=!0,!s.shallow||!u&&!1!==s.renderRootComponent){if(w===t.Fragment){var j=[];return _(j,e.props.children),y(j,n,s,!1!==s.shallowHighOrder,p,d)}var F,A=e.__c=v(e,n);t.options.__b&&t.options.__b(e);var T=t.options.__r;if(w.prototype&&\"function\"==typeof w.prototype.render){var H=g(w,n);(A=e.__c=new w(C,H)).__v=e,A._dirty=A.__d=!0,A.props=C,null==A.state&&(A.state={}),null==A._nextState&&null==A.__s&&(A._nextState=A.__s=A.state),A.context=H,w.getDerivedStateFromProps?A.state=Object.assign({},A.state,w.getDerivedStateFromProps(A.props,A.state)):A.componentWillMount&&(A.componentWillMount(),A.state=A._nextState!==A.state?A._nextState:A.__s!==A.state?A.__s:A.state),T&&T(e),F=A.render(A.props,A.state,A.context)}else for(var M=g(w,n),L=0;A.__d&&L++<25;)A.__d=!1,T&&T(e),F=w.call(e.__c,C,M);return A.getChildContext&&(n=Object.assign({},n,A.getChildContext())),t.options.diffed&&t.options.diffed(e),y(F,n,s,!1!==s.shallowHighOrder,p,d)}w=(S=w).displayName||S!==Function&&S.name||function(e){var t=(Function.prototype.toString.call(e).match(/^\\s*function\\s+([^( ]+)/)||\"\")[1];if(!t){for(var n=-1,r=h.length;r--;)if(h[r]===e){n=r;break}n<0&&(n=h.push(e)-1),t=\"UnnamedComponent\"+n}return t}(S)}var E,$,D=\"<\"+w;if(C){var N=Object.keys(C);s&&!0===s.sortAttributes&&N.sort();for(var P=0;P<N.length;P++){var R=N[P],W=C[R];if(\"children\"!==R){if(!o.test(R)&&(s&&s.allAttributes||\"key\"!==R&&\"ref\"!==R&&\"__self\"!==R&&\"__source\"!==R)){if(\"defaultValue\"===R)R=\"value\";else if(\"defaultChecked\"===R)R=\"checked\";else if(\"defaultSelected\"===R)R=\"selected\";else if(\"className\"===R){if(void 0!==C.class)continue;R=\"class\"}else p&&i.test(R)&&(R=R.toLowerCase().replace(/^xlink:?/,\"xlink:\"));if(\"htmlFor\"===R){if(C.for)continue;R=\"for\"}\"style\"===R&&W&&\"object\"==typeof W&&(W=c(W)),\"a\"===R[0]&&\"r\"===R[1]&&\"boolean\"==typeof W&&(W=String(W));var q=s.attributeHook&&s.attributeHook(R,W,n,s,O);if(q||\"\"===q)D+=q;else if(\"dangerouslySetInnerHTML\"===R)$=W&&W.__html;else if(\"textarea\"===w&&\"value\"===R)E=W;else if((W||0===W||\"\"===W)&&\"function\"!=typeof W){if(!(!0!==W&&\"\"!==W||(W=R,s&&s.xml))){D=D+\" \"+R;continue}if(\"value\"===R){if(\"select\"===w){d=W;continue}\"option\"===w&&d==W&&void 0===C.selected&&(D+=\" selected\")}D=D+\" \"+R+'=\"'+a(W)+'\"'}}}else E=W}}if(m){var I=D.replace(/\\n\\s*/,\" \");I===D||~I.indexOf(\"\\n\")?m&&~D.indexOf(\"\\n\")&&(D+=\"\\n\"):D=I}if(D+=\">\",o.test(w))throw new Error(w+\" is not a valid HTML tag name in \"+D);var U,V=r.test(w)||s.voidElements&&s.voidElements.test(w),z=[];if($)m&&f($)&&($=\"\\n\"+b+l($,b)),D+=$;else if(null!=E&&_(U=[],E).length){for(var Z=m&&~D.indexOf(\"\\n\"),B=!1,G=0;G<U.length;G++){var J=U[G];if(null!=J&&!1!==J){var K=y(J,n,s,!0,\"svg\"===w||\"foreignObject\"!==w&&p,d);if(m&&!Z&&f(K)&&(Z=!0),K)if(m){var Q=K.length>0&&\"<\"!=K[0];B&&Q?z[z.length-1]+=K:z.push(K),B=Q}else z.push(K)}}if(m&&Z)for(var X=z.length;X--;)z[X]=\"\\n\"+b+l(z[X],b)}if(z.length||$)D+=z.join(\"\");else if(s&&s.xml)return D.substring(0,D.length-1)+\" />\";return!V||U||$?(m&&~D.indexOf(\"\\n\")&&(D+=\"\\n\"),D=D+\"</\"+w+\">\"):D=D.replace(/>$/,\" />\"),D}var m={shallow:!0};k.render=k;var b=function(e,t){return k(e,t,m)},x=[];function k(e,n,r){n=n||{};var o=t.options.__s;t.options.__s=!0;var i,s=t.h(t.Fragment,null);return s.__k=[e],i=r&&(r.pretty||r.voidElements||r.sortAttributes||r.shallow||r.allAttributes||r.xml||r.attributeHook)?y(e,n,r):F(e,n,!1,void 0,s),t.options.__c&&t.options.__c(e,x),t.options.__s=o,x.length=0,i}function S(e){return null==e||\"boolean\"==typeof e?null:\"string\"==typeof e||\"number\"==typeof e||\"bigint\"==typeof e?t.h(null,null,e):e}function w(e,t){return\"className\"===e?\"class\":\"htmlFor\"===e?\"for\":\"defaultValue\"===e?\"value\":\"defaultChecked\"===e?\"checked\":\"defaultSelected\"===e?\"selected\":t&&i.test(e)?e.toLowerCase().replace(/^xlink:?/,\"xlink:\"):e}function C(e,t){return\"style\"===e&&null!=t&&\"object\"==typeof t?c(t):\"a\"===e[0]&&\"r\"===e[1]&&\"boolean\"==typeof t?String(t):t}var O=Array.isArray,j=Object.assign;function F(e,n,i,s,l){if(null==e||!0===e||!1===e||\"\"===e)return\"\";if(\"object\"!=typeof e)return\"function\"==typeof e?\"\":a(e);if(O(e)){var f=\"\";l.__k=e;for(var u=0;u<e.length;u++)f+=F(e[u],n,i,s,l),e[u]=S(e[u]);return f}if(void 0!==e.constructor)return\"\";e.__=l,t.options.__b&&t.options.__b(e);var p=e.type,c=e.props;if(\"function\"==typeof p){var _;if(p===t.Fragment)_=c.children;else{_=p.prototype&&\"function\"==typeof p.prototype.render?function(e,n){var r=e.type,o=g(r,n),i=new r(e.props,o);e.__c=i,i.__v=e,i.__d=!0,i.props=e.props,null==i.state&&(i.state={}),null==i.__s&&(i.__s=i.state),i.context=o,r.getDerivedStateFromProps?i.state=j({},i.state,r.getDerivedStateFromProps(i.props,i.state)):i.componentWillMount&&(i.componentWillMount(),i.state=i.__s!==i.state?i.__s:i.state);var s=t.options.__r;return s&&s(e),i.render(i.props,i.state,i.context)}(e,n):function(e,n){var r,o=v(e,n),i=g(e.type,n);e.__c=o;for(var s=t.options.__r,a=0;o.__d&&a++<25;)o.__d=!1,s&&s(e),r=e.type.call(o,e.props,i);return r}(e,n);var d=e.__c;d.getChildContext&&(n=j({},n,d.getChildContext()))}var h=F(_=null!=_&&_.type===t.Fragment&&null==_.key?_.props.children:_,n,i,s,e);return t.options.diffed&&t.options.diffed(e),e.__=void 0,t.options.unmount&&t.options.unmount(e),h}var y,m,b=\"<\";if(b+=p,c)for(var x in y=c.children,c){var k=c[x];if(!(\"key\"===x||\"ref\"===x||\"__self\"===x||\"__source\"===x||\"children\"===x||\"className\"===x&&\"class\"in c||\"htmlFor\"===x&&\"for\"in c||o.test(x)))if(k=C(x=w(x,i),k),\"dangerouslySetInnerHTML\"===x)m=k&&k.__html;else if(\"textarea\"===p&&\"value\"===x)y=k;else if((k||0===k||\"\"===k)&&\"function\"!=typeof k){if(!0===k||\"\"===k){k=x,b=b+\" \"+x;continue}if(\"value\"===x){if(\"select\"===p){s=k;continue}\"option\"!==p||s!=k||\"selected\"in c||(b+=\" selected\")}b=b+\" \"+x+'=\"'+a(k)+'\"'}}var A=b;if(b+=\">\",o.test(p))throw new Error(p+\" is not a valid HTML tag name in \"+b);var T=\"\",H=!1;if(m)T+=m,H=!0;else if(\"string\"==typeof y)T+=a(y),H=!0;else if(O(y)){e.__k=y;for(var M=0;M<y.length;M++){var L=y[M];if(y[M]=S(L),null!=L&&!1!==L){var E=F(L,n,\"svg\"===p||\"foreignObject\"!==p&&i,s,e);E&&(T+=E,H=!0)}}}else if(null!=y&&!1!==y&&!0!==y){e.__k=[S(y)];var $=F(y,n,\"svg\"===p||\"foreignObject\"!==p&&i,s,e);$&&(T+=$,H=!0)}if(t.options.diffed&&t.options.diffed(e),e.__=void 0,t.options.unmount&&t.options.unmount(e),H)b+=T;else if(r.test(p))return A+\" />\";return b+\"</\"+p+\">\"}k.shallowRender=b,e.default=k,e.render=k,e.renderToStaticMarkup=k,e.renderToString=k,e.shallowRender=b});\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/preact-render-to-string/dist/commonjs.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/preact-render-to-string/dist/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/preact-render-to-string/dist/index.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./commonjs */ \"(rsc)/./node_modules/next-auth/node_modules/preact-render-to-string/dist/commonjs.js\")[\"default\"];//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy9wcmVhY3QtcmVuZGVyLXRvLXN0cmluZy9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLHlKQUE4QyIsInNvdXJjZXMiOlsiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvbm9kZV9tb2R1bGVzL3ByZWFjdC1yZW5kZXItdG8tc3RyaW5nL2Rpc3QvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2NvbW1vbmpzJykuZGVmYXVsdDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/preact-render-to-string/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/preact/dist/preact.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var n,l,t,u,r,i,o,e,f,c,s,p,a,h={},v=[],y=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,w=Array.isArray;function d(n,l){for(var t in l)n[t]=l[t];return n}function g(n){n&&n.parentNode&&n.parentNode.removeChild(n)}function _(l,t,u){var r,i,o,e={};for(o in t)\"key\"==o?r=t[o]:\"ref\"==o?i=t[o]:e[o]=t[o];if(arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):u),\"function\"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps)void 0===e[o]&&(e[o]=l.defaultProps[o]);return x(l,e,r,i,null)}function x(n,u,r,i,o){var e={type:n,props:u,key:r,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==o?++t:o,__i:-1,__u:0};return null==o&&null!=l.vnode&&l.vnode(e),e}function m(n){return n.children}function b(n,l){this.props=n,this.context=l}function k(n,l){if(null==l)return n.__?k(n.__,n.__i+1):null;for(var t;l<n.__k.length;l++)if(null!=(t=n.__k[l])&&null!=t.__e)return t.__e;return\"function\"==typeof n.type?k(n):null}function S(n){var l,t;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(t=n.__k[l])&&null!=t.__e){n.__e=n.__c.base=t.__e;break}return S(n)}}function M(n){(!n.__d&&(n.__d=!0)&&r.push(n)&&!$.__r++||i!=l.debounceRendering)&&((i=l.debounceRendering)||o)($)}function $(){for(var n,t,u,i,o,f,c,s=1;r.length;)r.length>s&&r.sort(e),n=r.shift(),s=r.length,n.__d&&(u=void 0,o=(i=(t=n).__v).__e,f=[],c=[],t.__P&&((u=d({},i)).__v=i.__v+1,l.vnode&&l.vnode(u),j(t.__P,u,i,t.__n,t.__P.namespaceURI,32&i.__u?[o]:null,f,null==o?k(i):o,!!(32&i.__u),c),u.__v=i.__v,u.__.__k[u.__i]=u,F(f,u,c),u.__e!=o&&S(u)));$.__r=0}function C(n,l,t,u,r,i,o,e,f,c,s){var p,a,y,w,d,g,_=u&&u.__k||v,x=l.length;for(f=I(t,l,_,f,x),p=0;p<x;p++)null!=(y=t.__k[p])&&(a=-1==y.__i?h:_[y.__i]||h,y.__i=p,g=j(n,y,a,r,i,o,e,f,c,s),w=y.__e,y.ref&&a.ref!=y.ref&&(a.ref&&N(a.ref,null,y),s.push(y.ref,y.__c||w,y)),null==d&&null!=w&&(d=w),4&y.__u||a.__k===y.__k?f=P(y,f,n):\"function\"==typeof y.type&&void 0!==g?f=g:w&&(f=w.nextSibling),y.__u&=-7);return t.__e=d,f}function I(n,l,t,u,r){var i,o,e,f,c,s=t.length,p=s,a=0;for(n.__k=new Array(r),i=0;i<r;i++)null!=(o=l[i])&&\"boolean\"!=typeof o&&\"function\"!=typeof o?(f=i+a,(o=n.__k[i]=\"string\"==typeof o||\"number\"==typeof o||\"bigint\"==typeof o||o.constructor==String?x(null,o,null,null,null):w(o)?x(m,{children:o},null,null,null):null==o.constructor&&o.__b>0?x(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=n,o.__b=n.__b+1,e=null,-1!=(c=o.__i=A(o,t,f,p))&&(p--,(e=t[c])&&(e.__u|=2)),null==e||null==e.__v?(-1==c&&(r>s?a--:r<s&&a++),\"function\"!=typeof o.type&&(o.__u|=4)):c!=f&&(c==f-1?a--:c==f+1?a++:(c>f?a--:a++,o.__u|=4))):n.__k[i]=null;if(p)for(i=0;i<s;i++)null!=(e=t[i])&&0==(2&e.__u)&&(e.__e==u&&(u=k(e)),V(e,e));return u}function P(n,l,t){var u,r;if(\"function\"==typeof n.type){for(u=n.__k,r=0;u&&r<u.length;r++)u[r]&&(u[r].__=n,l=P(u[r],l,t));return l}n.__e!=l&&(l&&n.type&&!t.contains(l)&&(l=k(n)),t.insertBefore(n.__e,l||null),l=n.__e);do{l=l&&l.nextSibling}while(null!=l&&8==l.nodeType);return l}function A(n,l,t,u){var r,i,o=n.key,e=n.type,f=l[t];if(null===f&&null==n.key||f&&o==f.key&&e==f.type&&0==(2&f.__u))return t;if(u>(null!=f&&0==(2&f.__u)?1:0))for(r=t-1,i=t+1;r>=0||i<l.length;){if(r>=0){if((f=l[r])&&0==(2&f.__u)&&o==f.key&&e==f.type)return r;r--}if(i<l.length){if((f=l[i])&&0==(2&f.__u)&&o==f.key&&e==f.type)return i;i++}}return-1}function H(n,l,t){\"-\"==l[0]?n.setProperty(l,null==t?\"\":t):n[l]=null==t?\"\":\"number\"!=typeof t||y.test(l)?t:t+\"px\"}function L(n,l,t,u,r){var i,o;n:if(\"style\"==l)if(\"string\"==typeof t)n.style.cssText=t;else{if(\"string\"==typeof u&&(n.style.cssText=u=\"\"),u)for(l in u)t&&l in t||H(n.style,l,\"\");if(t)for(l in t)u&&t[l]==u[l]||H(n.style,l,t[l])}else if(\"o\"==l[0]&&\"n\"==l[1])i=l!=(l=l.replace(f,\"$1\")),o=l.toLowerCase(),l=o in n||\"onFocusOut\"==l||\"onFocusIn\"==l?o.slice(2):l.slice(2),n.l||(n.l={}),n.l[l+i]=t,t?u?t.t=u.t:(t.t=c,n.addEventListener(l,i?p:s,i)):n.removeEventListener(l,i?p:s,i);else{if(\"http://www.w3.org/2000/svg\"==r)l=l.replace(/xlink(H|:h)/,\"h\").replace(/sName$/,\"s\");else if(\"width\"!=l&&\"height\"!=l&&\"href\"!=l&&\"list\"!=l&&\"form\"!=l&&\"tabIndex\"!=l&&\"download\"!=l&&\"rowSpan\"!=l&&\"colSpan\"!=l&&\"role\"!=l&&\"popover\"!=l&&l in n)try{n[l]=null==t?\"\":t;break n}catch(n){}\"function\"==typeof t||(null==t||!1===t&&\"-\"!=l[4]?n.removeAttribute(l):n.setAttribute(l,\"popover\"==l&&1==t?\"\":t))}}function T(n){return function(t){if(this.l){var u=this.l[t.type+n];if(null==t.u)t.u=c++;else if(t.u<u.t)return;return u(l.event?l.event(t):t)}}}function j(n,t,u,r,i,o,e,f,c,s){var p,a,h,v,y,_,x,k,S,M,$,I,P,A,H,L,T,j=t.type;if(null!=t.constructor)return null;128&u.__u&&(c=!!(32&u.__u),o=[f=t.__e=u.__e]),(p=l.__b)&&p(t);n:if(\"function\"==typeof j)try{if(k=t.props,S=\"prototype\"in j&&j.prototype.render,M=(p=j.contextType)&&r[p.__c],$=p?M?M.props.value:p.__:r,u.__c?x=(a=t.__c=u.__c).__=a.__E:(S?t.__c=a=new j(k,$):(t.__c=a=new b(k,$),a.constructor=j,a.render=q),M&&M.sub(a),a.props=k,a.state||(a.state={}),a.context=$,a.__n=r,h=a.__d=!0,a.__h=[],a._sb=[]),S&&null==a.__s&&(a.__s=a.state),S&&null!=j.getDerivedStateFromProps&&(a.__s==a.state&&(a.__s=d({},a.__s)),d(a.__s,j.getDerivedStateFromProps(k,a.__s))),v=a.props,y=a.state,a.__v=t,h)S&&null==j.getDerivedStateFromProps&&null!=a.componentWillMount&&a.componentWillMount(),S&&null!=a.componentDidMount&&a.__h.push(a.componentDidMount);else{if(S&&null==j.getDerivedStateFromProps&&k!==v&&null!=a.componentWillReceiveProps&&a.componentWillReceiveProps(k,$),!a.__e&&null!=a.shouldComponentUpdate&&!1===a.shouldComponentUpdate(k,a.__s,$)||t.__v==u.__v){for(t.__v!=u.__v&&(a.props=k,a.state=a.__s,a.__d=!1),t.__e=u.__e,t.__k=u.__k,t.__k.some(function(n){n&&(n.__=t)}),I=0;I<a._sb.length;I++)a.__h.push(a._sb[I]);a._sb=[],a.__h.length&&e.push(a);break n}null!=a.componentWillUpdate&&a.componentWillUpdate(k,a.__s,$),S&&null!=a.componentDidUpdate&&a.__h.push(function(){a.componentDidUpdate(v,y,_)})}if(a.context=$,a.props=k,a.__P=n,a.__e=!1,P=l.__r,A=0,S){for(a.state=a.__s,a.__d=!1,P&&P(t),p=a.render(a.props,a.state,a.context),H=0;H<a._sb.length;H++)a.__h.push(a._sb[H]);a._sb=[]}else do{a.__d=!1,P&&P(t),p=a.render(a.props,a.state,a.context),a.state=a.__s}while(a.__d&&++A<25);a.state=a.__s,null!=a.getChildContext&&(r=d(d({},r),a.getChildContext())),S&&!h&&null!=a.getSnapshotBeforeUpdate&&(_=a.getSnapshotBeforeUpdate(v,y)),L=p,null!=p&&p.type===m&&null==p.key&&(L=O(p.props.children)),f=C(n,w(L)?L:[L],t,u,r,i,o,e,f,c,s),a.base=t.__e,t.__u&=-161,a.__h.length&&e.push(a),x&&(a.__E=a.__=null)}catch(n){if(t.__v=null,c||null!=o)if(n.then){for(t.__u|=c?160:128;f&&8==f.nodeType&&f.nextSibling;)f=f.nextSibling;o[o.indexOf(f)]=null,t.__e=f}else for(T=o.length;T--;)g(o[T]);else t.__e=u.__e,t.__k=u.__k;l.__e(n,t,u)}else null==o&&t.__v==u.__v?(t.__k=u.__k,t.__e=u.__e):f=t.__e=z(u.__e,t,u,r,i,o,e,c,s);return(p=l.diffed)&&p(t),128&t.__u?void 0:f}function F(n,t,u){for(var r=0;r<u.length;r++)N(u[r],u[++r],u[++r]);l.__c&&l.__c(t,n),n.some(function(t){try{n=t.__h,t.__h=[],n.some(function(n){n.call(t)})}catch(n){l.__e(n,t.__v)}})}function O(n){return\"object\"!=typeof n||null==n||n.__b&&n.__b>0?n:w(n)?n.map(O):d({},n)}function z(t,u,r,i,o,e,f,c,s){var p,a,v,y,d,_,x,m=r.props,b=u.props,S=u.type;if(\"svg\"==S?o=\"http://www.w3.org/2000/svg\":\"math\"==S?o=\"http://www.w3.org/1998/Math/MathML\":o||(o=\"http://www.w3.org/1999/xhtml\"),null!=e)for(p=0;p<e.length;p++)if((d=e[p])&&\"setAttribute\"in d==!!S&&(S?d.localName==S:3==d.nodeType)){t=d,e[p]=null;break}if(null==t){if(null==S)return document.createTextNode(b);t=document.createElementNS(o,S,b.is&&b),c&&(l.__m&&l.__m(u,e),c=!1),e=null}if(null==S)m===b||c&&t.data==b||(t.data=b);else{if(e=e&&n.call(t.childNodes),m=r.props||h,!c&&null!=e)for(m={},p=0;p<t.attributes.length;p++)m[(d=t.attributes[p]).name]=d.value;for(p in m)if(d=m[p],\"children\"==p);else if(\"dangerouslySetInnerHTML\"==p)v=d;else if(!(p in b)){if(\"value\"==p&&\"defaultValue\"in b||\"checked\"==p&&\"defaultChecked\"in b)continue;L(t,p,null,d,o)}for(p in b)d=b[p],\"children\"==p?y=d:\"dangerouslySetInnerHTML\"==p?a=d:\"value\"==p?_=d:\"checked\"==p?x=d:c&&\"function\"!=typeof d||m[p]===d||L(t,p,d,m[p],o);if(a)c||v&&(a.__html==v.__html||a.__html==t.innerHTML)||(t.innerHTML=a.__html),u.__k=[];else if(v&&(t.innerHTML=\"\"),C(\"template\"==u.type?t.content:t,w(y)?y:[y],u,r,i,\"foreignObject\"==S?\"http://www.w3.org/1999/xhtml\":o,e,f,e?e[0]:r.__k&&k(r,0),c,s),null!=e)for(p=e.length;p--;)g(e[p]);c||(p=\"value\",\"progress\"==S&&null==_?t.removeAttribute(\"value\"):null!=_&&(_!==t[p]||\"progress\"==S&&!_||\"option\"==S&&_!=m[p])&&L(t,p,_,m[p],o),p=\"checked\",null!=x&&x!=t[p]&&L(t,p,x,m[p],o))}return t}function N(n,t,u){try{if(\"function\"==typeof n){var r=\"function\"==typeof n.__u;r&&n.__u(),r&&null==t||(n.__u=n(t))}else n.current=t}catch(n){l.__e(n,u)}}function V(n,t,u){var r,i;if(l.unmount&&l.unmount(n),(r=n.ref)&&(r.current&&r.current!=n.__e||N(r,null,t)),null!=(r=n.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(n){l.__e(n,t)}r.base=r.__P=null}if(r=n.__k)for(i=0;i<r.length;i++)r[i]&&V(r[i],t,u||\"function\"!=typeof n.type);u||g(n.__e),n.__c=n.__=n.__e=void 0}function q(n,l,t){return this.constructor(n,t)}function B(t,u,r){var i,o,e,f;u==document&&(u=document.documentElement),l.__&&l.__(t,u),o=(i=\"function\"==typeof r)?null:r&&r.__k||u.__k,e=[],f=[],j(u,t=(!i&&r||u).__k=_(m,null,[t]),o||h,h,u.namespaceURI,!i&&r?[r]:o?null:u.firstChild?n.call(u.childNodes):null,e,!i&&r?r:o?o.__e:u.firstChild,i,f),F(e,t,f)}n=v.slice,l={__e:function(n,l,t,u){for(var r,i,o;l=l.__;)if((r=l.__c)&&!r.__)try{if((i=r.constructor)&&null!=i.getDerivedStateFromError&&(r.setState(i.getDerivedStateFromError(n)),o=r.__d),null!=r.componentDidCatch&&(r.componentDidCatch(n,u||{}),o=r.__d),o)return r.__E=r}catch(l){n=l}throw n}},t=0,u=function(n){return null!=n&&null==n.constructor},b.prototype.setState=function(n,l){var t;t=null!=this.__s&&this.__s!=this.state?this.__s:this.__s=d({},this.state),\"function\"==typeof n&&(n=n(d({},t),this.props)),n&&d(t,n),null!=n&&this.__v&&(l&&this._sb.push(l),M(this))},b.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),M(this))},b.prototype.render=m,r=[],o=\"function\"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,e=function(n,l){return n.__v.__b-l.__v.__b},$.__r=0,f=/(PointerCapture)$|Capture$/i,c=0,s=T(!1),p=T(!0),a=0,exports.Component=b,exports.Fragment=m,exports.cloneElement=function(l,t,u){var r,i,o,e,f=d({},l.props);for(o in l.type&&l.type.defaultProps&&(e=l.type.defaultProps),t)\"key\"==o?r=t[o]:\"ref\"==o?i=t[o]:f[o]=void 0===t[o]&&null!=e?e[o]:t[o];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):u),x(l.type,f,r||l.key,i||l.ref,null)},exports.createContext=function(n){function l(n){var t,u;return this.getChildContext||(t=new Set,(u={})[l.__c]=this,this.getChildContext=function(){return u},this.componentWillUnmount=function(){t=null},this.shouldComponentUpdate=function(n){this.props.value!=n.value&&t.forEach(function(n){n.__e=!0,M(n)})},this.sub=function(n){t.add(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){t&&t.delete(n),l&&l.call(n)}}),n.children}return l.__c=\"__cC\"+a++,l.__=n,l.Provider=l.__l=(l.Consumer=function(n,l){return n.children(l)}).contextType=l,l},exports.createElement=_,exports.createRef=function(){return{current:null}},exports.h=_,exports.hydrate=function n(l,t){B(l,t,n)},exports.isValidElement=u,exports.options=l,exports.render=B,exports.toChildArray=function n(l,t){return t=t||[],null==l||\"boolean\"==typeof l||(w(l)?l.some(function(l){n(l,t)}):t.push(l)),t};\n//# sourceMappingURL=preact.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/preact/dist/preact.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = Credentials;\nfunction Credentials(options) {\n  return {\n    id: \"credentials\",\n    name: \"Credentials\",\n    type: \"credentials\",\n    credentials: {},\n    authorize: () => null,\n    options\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IENyZWRlbnRpYWxzO1xuZnVuY3Rpb24gQ3JlZGVudGlhbHMob3B0aW9ucykge1xuICByZXR1cm4ge1xuICAgIGlkOiBcImNyZWRlbnRpYWxzXCIsXG4gICAgbmFtZTogXCJDcmVkZW50aWFsc1wiLFxuICAgIHR5cGU6IFwiY3JlZGVudGlhbHNcIixcbiAgICBjcmVkZW50aWFsczoge30sXG4gICAgYXV0aG9yaXplOiAoKSA9PiBudWxsLFxuICAgIG9wdGlvbnNcbiAgfTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/github.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/github.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = Github;\nfunction Github(options) {\n  return {\n    id: \"github\",\n    name: \"GitHub\",\n    type: \"oauth\",\n    authorization: {\n      url: \"https://github.com/login/oauth/authorize\",\n      params: {\n        scope: \"read:user user:email\"\n      }\n    },\n    token: \"https://github.com/login/oauth/access_token\",\n    userinfo: {\n      url: \"https://api.github.com/user\",\n      async request({\n        client,\n        tokens\n      }) {\n        const profile = await client.userinfo(tokens.access_token);\n        if (!profile.email) {\n          const res = await fetch(\"https://api.github.com/user/emails\", {\n            headers: {\n              Authorization: `token ${tokens.access_token}`\n            }\n          });\n          if (res.ok) {\n            var _emails$find;\n            const emails = await res.json();\n            profile.email = ((_emails$find = emails.find(e => e.primary)) !== null && _emails$find !== void 0 ? _emails$find : emails[0]).email;\n          }\n        }\n        return profile;\n      }\n    },\n    profile(profile) {\n      var _profile$name;\n      return {\n        id: profile.id.toString(),\n        name: (_profile$name = profile.name) !== null && _profile$name !== void 0 ? _profile$name : profile.login,\n        email: profile.email,\n        image: profile.avatar_url\n      };\n    },\n    style: {\n      logo: \"/github.svg\",\n      bg: \"#24292f\",\n      text: \"#fff\"\n    },\n    options\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/github.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/google.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/google.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = Google;\nfunction Google(options) {\n  return {\n    id: \"google\",\n    name: \"Google\",\n    type: \"oauth\",\n    wellKnown: \"https://accounts.google.com/.well-known/openid-configuration\",\n    authorization: {\n      params: {\n        scope: \"openid email profile\"\n      }\n    },\n    idToken: true,\n    checks: [\"pkce\", \"state\"],\n    profile(profile) {\n      return {\n        id: profile.sub,\n        name: profile.name,\n        email: profile.email,\n        image: profile.picture\n      };\n    },\n    style: {\n      logo: \"/google.svg\",\n      bg: \"#fff\",\n      text: \"#000\"\n    },\n    options\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtYXV0aC9wcm92aWRlcnMvZ29vZ2xlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gR29vZ2xlO1xuZnVuY3Rpb24gR29vZ2xlKG9wdGlvbnMpIHtcbiAgcmV0dXJuIHtcbiAgICBpZDogXCJnb29nbGVcIixcbiAgICBuYW1lOiBcIkdvb2dsZVwiLFxuICAgIHR5cGU6IFwib2F1dGhcIixcbiAgICB3ZWxsS25vd246IFwiaHR0cHM6Ly9hY2NvdW50cy5nb29nbGUuY29tLy53ZWxsLWtub3duL29wZW5pZC1jb25maWd1cmF0aW9uXCIsXG4gICAgYXV0aG9yaXphdGlvbjoge1xuICAgICAgcGFyYW1zOiB7XG4gICAgICAgIHNjb3BlOiBcIm9wZW5pZCBlbWFpbCBwcm9maWxlXCJcbiAgICAgIH1cbiAgICB9LFxuICAgIGlkVG9rZW46IHRydWUsXG4gICAgY2hlY2tzOiBbXCJwa2NlXCIsIFwic3RhdGVcIl0sXG4gICAgcHJvZmlsZShwcm9maWxlKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBpZDogcHJvZmlsZS5zdWIsXG4gICAgICAgIG5hbWU6IHByb2ZpbGUubmFtZSxcbiAgICAgICAgZW1haWw6IHByb2ZpbGUuZW1haWwsXG4gICAgICAgIGltYWdlOiBwcm9maWxlLnBpY3R1cmVcbiAgICAgIH07XG4gICAgfSxcbiAgICBzdHlsZToge1xuICAgICAgbG9nbzogXCIvZ29vZ2xlLnN2Z1wiLFxuICAgICAgYmc6IFwiI2ZmZlwiLFxuICAgICAgdGV4dDogXCIjMDAwXCJcbiAgICB9LFxuICAgIG9wdGlvbnNcbiAgfTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/google.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/detect-origin.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/utils/detect-origin.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.detectOrigin = detectOrigin;\nfunction detectOrigin(forwardedHost, protocol) {\n  var _process$env$VERCEL;\n  if ((_process$env$VERCEL = process.env.VERCEL) !== null && _process$env$VERCEL !== void 0 ? _process$env$VERCEL : process.env.AUTH_TRUST_HOST) return `${protocol === \"http\" ? \"http\" : \"https\"}://${forwardedHost}`;\n  return process.env.NEXTAUTH_URL;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL2RldGVjdC1vcmlnaW4uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQSwySkFBMkosdUNBQXVDLEtBQUssY0FBYztBQUNyTjtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQtYXV0aC91dGlscy9kZXRlY3Qtb3JpZ2luLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZXRlY3RPcmlnaW4gPSBkZXRlY3RPcmlnaW47XG5mdW5jdGlvbiBkZXRlY3RPcmlnaW4oZm9yd2FyZGVkSG9zdCwgcHJvdG9jb2wpIHtcbiAgdmFyIF9wcm9jZXNzJGVudiRWRVJDRUw7XG4gIGlmICgoX3Byb2Nlc3MkZW52JFZFUkNFTCA9IHByb2Nlc3MuZW52LlZFUkNFTCkgIT09IG51bGwgJiYgX3Byb2Nlc3MkZW52JFZFUkNFTCAhPT0gdm9pZCAwID8gX3Byb2Nlc3MkZW52JFZFUkNFTCA6IHByb2Nlc3MuZW52LkFVVEhfVFJVU1RfSE9TVCkgcmV0dXJuIGAke3Byb3RvY29sID09PSBcImh0dHBcIiA/IFwiaHR0cFwiIDogXCJodHRwc1wifTovLyR7Zm9yd2FyZGVkSG9zdH1gO1xuICByZXR1cm4gcHJvY2Vzcy5lbnYuTkVYVEFVVEhfVVJMO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/detect-origin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/logger.js":
/*!************************************************!*\
  !*** ./node_modules/next-auth/utils/logger.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(rsc)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(rsc)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(rsc)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _errors = __webpack_require__(/*! ../core/errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports[\"default\"] = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/logger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/merge.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/utils/merge.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.merge = merge;\nfunction isObject(item) {\n  return item && typeof item === \"object\" && !Array.isArray(item);\n}\nfunction merge(target, ...sources) {\n  if (!sources.length) return target;\n  const source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (const key in source) {\n      if (isObject(source[key])) {\n        if (!target[key]) Object.assign(target, {\n          [key]: {}\n        });\n        merge(target[key], source[key]);\n      } else {\n        Object.assign(target, {\n          [key]: source[key]\n        });\n      }\n    }\n  }\n  return merge(target, ...sources);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL21lcmdlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvdXRpbHMvbWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLm1lcmdlID0gbWVyZ2U7XG5mdW5jdGlvbiBpc09iamVjdChpdGVtKSB7XG4gIHJldHVybiBpdGVtICYmIHR5cGVvZiBpdGVtID09PSBcIm9iamVjdFwiICYmICFBcnJheS5pc0FycmF5KGl0ZW0pO1xufVxuZnVuY3Rpb24gbWVyZ2UodGFyZ2V0LCAuLi5zb3VyY2VzKSB7XG4gIGlmICghc291cmNlcy5sZW5ndGgpIHJldHVybiB0YXJnZXQ7XG4gIGNvbnN0IHNvdXJjZSA9IHNvdXJjZXMuc2hpZnQoKTtcbiAgaWYgKGlzT2JqZWN0KHRhcmdldCkgJiYgaXNPYmplY3Qoc291cmNlKSkge1xuICAgIGZvciAoY29uc3Qga2V5IGluIHNvdXJjZSkge1xuICAgICAgaWYgKGlzT2JqZWN0KHNvdXJjZVtrZXldKSkge1xuICAgICAgICBpZiAoIXRhcmdldFtrZXldKSBPYmplY3QuYXNzaWduKHRhcmdldCwge1xuICAgICAgICAgIFtrZXldOiB7fVxuICAgICAgICB9KTtcbiAgICAgICAgbWVyZ2UodGFyZ2V0W2tleV0sIHNvdXJjZVtrZXldKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIE9iamVjdC5hc3NpZ24odGFyZ2V0LCB7XG4gICAgICAgICAgW2tleV06IHNvdXJjZVtrZXldXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICByZXR1cm4gbWVyZ2UodGFyZ2V0LCAuLi5zb3VyY2VzKTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/parse-url.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/utils/parse-url.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL3BhcnNlLXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLElBQUk7QUFDekI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFlBQVksRUFBRSxLQUFLO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL3BhcnNlLXVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IHBhcnNlVXJsO1xuZnVuY3Rpb24gcGFyc2VVcmwodXJsKSB7XG4gIHZhciBfdXJsMjtcbiAgY29uc3QgZGVmYXVsdFVybCA9IG5ldyBVUkwoXCJodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpL2F1dGhcIik7XG4gIGlmICh1cmwgJiYgIXVybC5zdGFydHNXaXRoKFwiaHR0cFwiKSkge1xuICAgIHVybCA9IGBodHRwczovLyR7dXJsfWA7XG4gIH1cbiAgY29uc3QgX3VybCA9IG5ldyBVUkwoKF91cmwyID0gdXJsKSAhPT0gbnVsbCAmJiBfdXJsMiAhPT0gdm9pZCAwID8gX3VybDIgOiBkZWZhdWx0VXJsKTtcbiAgY29uc3QgcGF0aCA9IChfdXJsLnBhdGhuYW1lID09PSBcIi9cIiA/IGRlZmF1bHRVcmwucGF0aG5hbWUgOiBfdXJsLnBhdGhuYW1lKS5yZXBsYWNlKC9cXC8kLywgXCJcIik7XG4gIGNvbnN0IGJhc2UgPSBgJHtfdXJsLm9yaWdpbn0ke3BhdGh9YDtcbiAgcmV0dXJuIHtcbiAgICBvcmlnaW46IF91cmwub3JpZ2luLFxuICAgIGhvc3Q6IF91cmwuaG9zdCxcbiAgICBwYXRoLFxuICAgIGJhc2UsXG4gICAgdG9TdHJpbmc6ICgpID0+IGJhc2VcbiAgfTtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/parse-url.js\n");

/***/ })

};
;