/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_wood_workspace_aitools_aitools_website_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_wood_workspace_aitools_aitools_website_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDa0I7QUFFbkQsTUFBTUUsVUFBVUYsZ0RBQVFBLENBQUNDLGtEQUFXQTtBQUVPIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvc3JjL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOZXh0QXV0aCBmcm9tICduZXh0LWF1dGgnO1xuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICcuLi8uLi8uLi8uLi9saWIvYXV0aCc7XG5cbmNvbnN0IGhhbmRsZXIgPSBOZXh0QXV0aChhdXRoT3B0aW9ucyk7XG5cbmV4cG9ydCB7IGhhbmRsZXIgYXMgR0VULCBoYW5kbGVyIGFzIFBPU1QgfTtcbiJdLCJuYW1lcyI6WyJOZXh0QXV0aCIsImF1dGhPcHRpb25zIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _mongodb__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/User */ \"(rsc)/./src/models/User.ts\");\n\n\n\n\n\nconst authOptions = {\n    // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略\n    // adapter: MongoDBAdapter(client),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            id: 'email-code',\n            name: 'Email Code',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                code: {\n                    label: 'Code',\n                    type: 'text'\n                },\n                token: {\n                    label: 'Token',\n                    type: 'text'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.code || !credentials?.token) {\n                    return null;\n                }\n                try {\n                    await (0,_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n                    // 查找用户\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOne({\n                        email: credentials.email.toLowerCase(),\n                        emailVerificationExpires: {\n                            $gt: new Date()\n                        }\n                    });\n                    if (!user) {\n                        return null;\n                    }\n                    // 验证token和验证码\n                    const storedData = user.emailVerificationToken;\n                    if (!storedData || !storedData.includes(':')) {\n                        return null;\n                    }\n                    const [storedToken, storedCode] = storedData.split(':');\n                    if (storedToken !== credentials.token || storedCode !== credentials.code) {\n                        return null;\n                    }\n                    // 验证成功，更新用户状态\n                    user.emailVerified = true;\n                    user.emailVerificationToken = undefined;\n                    user.emailVerificationExpires = undefined;\n                    user.lastLoginAt = new Date();\n                    // 如果用户没有邮箱账户记录，添加一个\n                    const hasEmailAccount = user.accounts.some((acc)=>acc.provider === 'email');\n                    if (!hasEmailAccount) {\n                        user.accounts.push({\n                            provider: 'email',\n                            providerId: 'email',\n                            providerAccountId: user.email\n                        });\n                    }\n                    await user.save();\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        name: user.name,\n                        image: user.avatar,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Email code authorization error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            // 对于credentials provider，用户已经在authorize中处理过了\n            if (account?.provider === 'email-code') {\n                return true;\n            }\n            await (0,_mongodb__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n            try {\n                // 查找或创建用户（仅用于OAuth providers）\n                let existingUser = await _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOne({\n                    email: user.email\n                });\n                if (!existingUser) {\n                    // 创建新用户\n                    existingUser = new _models_User__WEBPACK_IMPORTED_MODULE_4__[\"default\"]({\n                        email: user.email,\n                        name: user.name || profile?.name || 'User',\n                        avatar: user.image || profile?.image,\n                        emailVerified: true,\n                        lastLoginAt: new Date()\n                    });\n                    await existingUser.save();\n                } else {\n                    // 更新最后登录时间\n                    existingUser.lastLoginAt = new Date();\n                    await existingUser.save();\n                }\n                // 添加或更新账户信息\n                if (account && account.provider !== 'email-code') {\n                    existingUser.addAccount({\n                        provider: account.provider,\n                        providerId: account.provider,\n                        providerAccountId: account.providerAccountId || account.id || '',\n                        accessToken: account.access_token,\n                        refreshToken: account.refresh_token,\n                        expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined\n                    });\n                    await existingUser.save();\n                }\n                return true;\n            } catch (error) {\n                console.error('Sign in error:', error);\n                return false;\n            }\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                // 对于credentials provider，user对象已经包含了我们需要的信息\n                token.userId = user.id;\n                token.role = user.role || 'user';\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token && session.user) {\n                session.user.id = token.userId;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        error: '/auth/error'\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function dbConnect() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AccountSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    provider: {\n        type: String,\n        required: true,\n        enum: [\n            'google',\n            'github',\n            'email'\n        ]\n    },\n    providerId: {\n        type: String,\n        required: true\n    },\n    providerAccountId: {\n        type: String,\n        required: true\n    },\n    accessToken: String,\n    refreshToken: String,\n    expiresAt: Date\n}, {\n    _id: false\n});\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    email: {\n        type: String,\n        required: [\n            true,\n            'Email is required'\n        ],\n        unique: true,\n        trim: true,\n        lowercase: true,\n        validate: {\n            validator: function(v) {\n                return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n            },\n            message: 'Please enter a valid email address'\n        }\n    },\n    name: {\n        type: String,\n        required: [\n            true,\n            'Name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Name cannot exceed 100 characters'\n        ]\n    },\n    avatar: {\n        type: String,\n        trim: true\n    },\n    role: {\n        type: String,\n        required: true,\n        enum: [\n            'user',\n            'admin'\n        ],\n        default: 'user'\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    // 认证相关\n    emailVerified: {\n        type: Boolean,\n        default: false\n    },\n    emailVerificationToken: {\n        type: String,\n        trim: true\n    },\n    emailVerificationExpires: {\n        type: Date\n    },\n    // OAuth账户关联\n    accounts: [\n        AccountSchema\n    ],\n    // 用户行为\n    submittedTools: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: 'Tool'\n        }\n    ],\n    likedTools: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: 'Tool'\n        }\n    ],\n    comments: [\n        {\n            type: mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema.Types.ObjectId,\n            ref: 'Comment'\n        }\n    ],\n    // 时间戳\n    lastLoginAt: {\n        type: Date\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\nUserSchema.index({\n    email: 1\n});\nUserSchema.index({\n    role: 1\n});\nUserSchema.index({\n    emailVerificationToken: 1\n});\nUserSchema.index({\n    'accounts.provider': 1,\n    'accounts.providerAccountId': 1\n});\n// 实例方法\nUserSchema.methods.addAccount = function(account) {\n    // 检查是否已存在相同的账户\n    const existingAccount = this.accounts.find((acc)=>acc.provider === account.provider && acc.providerAccountId === account.providerAccountId);\n    if (!existingAccount) {\n        this.accounts.push(account);\n    } else {\n        // 更新现有账户信息\n        Object.assign(existingAccount, account);\n    }\n};\nUserSchema.methods.removeAccount = function(provider, providerAccountId) {\n    this.accounts = this.accounts.filter((acc)=>!(acc.provider === provider && acc.providerAccountId === providerAccountId));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/@babel","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/oidc-token-hash","vendor-chunks/object-hash","vendor-chunks/lru-cache","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();