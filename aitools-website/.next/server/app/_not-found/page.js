/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(rsc)/./src/components/providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"92469030c7f7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjkyNDY5MDMwYzdmN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI工具导航 - 发现最好的人工智能工具\",\n    description: \"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。\",\n    keywords: \"AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用\",\n    authors: [\n        {\n            name: \"AI工具导航团队\"\n        }\n    ],\n    creator: \"AI工具导航\",\n    publisher: \"AI工具导航\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        type: 'website',\n        locale: 'zh_CN',\n        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',\n        siteName: 'AI工具导航',\n        title: 'AI工具导航 - 发现最好的人工智能工具',\n        description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'AI工具导航 - 发现最好的人工智能工具'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'AI工具导航 - 发现最好的人工智能工具',\n        description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。',\n        images: [\n            '/og-image.jpg'\n        ]\n    },\n    alternates: {\n        canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com'\n    },\n    verification: {\n        google: 'your-google-verification-code'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRndvb2QlMkZ3b3Jrc3BhY2UlMkZhaXRvb2xzJTJGYWl0b29scy13ZWJzaXRlJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ3b29kJTJGd29ya3NwYWNlJTJGYWl0b29scyUyRmFpdG9vbHMtd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZodHRwLWFjY2Vzcy1mYWxsYmFjayUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRm1ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBMEk7QUFDMUk7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSwwT0FBNkk7QUFDN0k7QUFDQSxvUkFBa0s7QUFDbEs7QUFDQSx3T0FBNEk7QUFDNUk7QUFDQSw0UEFBc0o7QUFDdEo7QUFDQSxrUUFBeUo7QUFDeko7QUFDQSxzUUFBMkoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9hc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGd29vZCUyRndvcmtzcGFjZSUyRmFpdG9vbHMlMkZhaXRvb2xzLXdlYnNpdGUlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkdlaXN0JTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3Qtc2FucyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0U2FucyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRndvb2QlMkZ3b3Jrc3BhY2UlMkZhaXRvb2xzJTJGYWl0b29scy13ZWJzaXRlJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJHZWlzdF9Nb25vJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIydmFyaWFibGUlNUMlMjIlM0ElNUMlMjItLWZvbnQtZ2Vpc3QtbW9ubyU1QyUyMiUyQyU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRndvb2QlMkZ3b3Jrc3BhY2UlMkZhaXRvb2xzJTJGYWl0b29scy13ZWJzaXRlJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ3b29kJTJGd29ya3NwYWNlJTJGYWl0b29scyUyRmFpdG9vbHMtd2Vic2l0ZSUyRnNyYyUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZIZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRndvb2QlMkZ3b3Jrc3BhY2UlMkZhaXRvb2xzJTJGYWl0b29scy13ZWJzaXRlJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlNlc3Npb25Qcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBZ0o7QUFDaEo7QUFDQSx3TUFBNEoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvc3JjL2NvbXBvbmVudHMvbGF5b3V0L0hlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzL1Nlc3Npb25Qcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/LoginModal.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/LoginModal.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FaEnvelope,FaGithub,FaGoogle,FaTimes!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction LoginModal({ isOpen, onClose }) {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('method');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationToken, setVerificationToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailError, setEmailError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const showToast = (message, type = 'success')=>{\n        // Simple toast implementation\n        const toast = document.createElement('div');\n        toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;\n        toast.textContent = message;\n        document.body.appendChild(toast);\n        setTimeout(()=>document.body.removeChild(toast), 3000);\n    };\n    const handleClose = ()=>{\n        setStep('method');\n        setEmail('');\n        setVerificationToken('');\n        setEmailError('');\n        onClose();\n    };\n    const handleOAuthLogin = async (provider)=>{\n        try {\n            setIsLoading(true);\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)(provider, {\n                callbackUrl: '/'\n            });\n        } catch (error) {\n            showToast('登录失败，请稍后重试', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEmailSubmit = async ()=>{\n        if (!email) {\n            setEmailError('请输入邮箱地址');\n            return;\n        }\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            setEmailError('请输入有效的邮箱地址');\n            return;\n        }\n        setEmailError('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/auth/send-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setVerificationToken(data.token);\n                setStep('code');\n                showToast('验证码已发送，请查看您的邮箱');\n            } else {\n                showToast(data.error || '发送失败，请稍后重试', 'error');\n            }\n        } catch (error) {\n            showToast('网络错误，请检查网络连接', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCodeVerify = async (code)=>{\n        if (code.length !== 6) return;\n        setIsLoading(true);\n        try {\n            const result = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)('email-code', {\n                email,\n                code,\n                token: verificationToken,\n                redirect: false\n            });\n            if (result?.ok) {\n                showToast('登录成功，欢迎回来！');\n                handleClose();\n            // NextAuth会自动更新session，不需要手动刷新页面\n            } else {\n                showToast(result?.error || '验证码错误', 'error');\n            }\n        } catch (error) {\n            showToast('网络错误，请检查网络连接', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderMethodStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: \"选择登录方式\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50\",\n                            onClick: ()=>handleOAuthLogin('google'),\n                            disabled: isLoading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGoogle, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 11\n                                }, this),\n                                \"使用 Google 登录\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                            onClick: ()=>handleOAuthLogin('github'),\n                            disabled: isLoading,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaGithub, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 11\n                                }, this),\n                                \"使用 GitHub 登录\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full border-t border-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-center text-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 bg-white text-gray-500\",\n                                children: \"或\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors\",\n                    onClick: ()=>setStep('email'),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaEnvelope, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, this),\n                        \"使用邮箱登录\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n            lineNumber: 119,\n            columnNumber: 5\n        }, this);\n    const renderEmailStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: \"输入您的邮箱地址，我们将发送验证码\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"邮箱地址\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"email\",\n                            value: email,\n                            onChange: (e)=>setEmail(e.target.value),\n                            placeholder: \"请输入邮箱地址\",\n                            onKeyPress: (e)=>e.key === 'Enter' && handleEmailSubmit(),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 9\n                        }, this),\n                        emailError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-red-600\",\n                            children: emailError\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\",\n                            onClick: handleEmailSubmit,\n                            disabled: isLoading,\n                            children: isLoading ? '发送中...' : '发送验证码'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                            onClick: ()=>setStep('method'),\n                            children: \"返回\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n            lineNumber: 164,\n            columnNumber: 5\n        }, this);\n    const handleCodeInputChange = (index, value)=>{\n        if (value.length > 1) return;\n        const inputs = document.querySelectorAll('.code-input');\n        inputs[index].value = value;\n        // Auto-focus next input\n        if (value && index < 5) {\n            inputs[index + 1]?.focus();\n        }\n        // Check if all inputs are filled\n        const code = Array.from(inputs).map((input)=>input.value).join('');\n        if (code.length === 6) {\n            handleCodeVerify(code);\n        }\n    };\n    const renderCodeStep = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: [\n                        \"请输入发送到 \",\n                        email,\n                        \" 的6位验证码\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"验证码\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-2\",\n                            children: [\n                                0,\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    maxLength: 1,\n                                    onChange: (e)=>handleCodeInputChange(index, e.target.value),\n                                    disabled: isLoading,\n                                    className: \"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50\"\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\",\n                            onClick: ()=>setStep('email'),\n                            children: \"重新发送验证码\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                            onClick: ()=>setStep('method'),\n                            children: \"返回\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n            lineNumber: 224,\n            columnNumber: 5\n        }, this);\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black bg-opacity-50\",\n                onClick: handleClose\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 text-center flex-1\",\n                                children: [\n                                    step === 'method' && '登录 AI Tools Directory',\n                                    step === 'email' && '邮箱登录',\n                                    step === 'code' && '输入验证码'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaEnvelope_FaGithub_FaGoogle_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_3__.FaTimes, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            step === 'method' && renderMethodStep(),\n                            step === 'email' && renderEmailStep(),\n                            step === 'code' && renderCodeStep()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/LoginModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/UserMenu.tsx":
/*!******************************************!*\
  !*** ./src/components/auth/UserMenu.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserMenu)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaChevronDown,FaCog,FaHeart,FaList,FaPlus,FaSignInAlt,FaSignOutAlt,FaUser!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _LoginModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LoginModal */ \"(ssr)/./src/components/auth/LoginModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction UserMenu() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [isLoginModalOpen, setIsLoginModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSignOut = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: '/'\n        });\n    };\n    const handleNavigation = (path)=>{\n        setIsMenuOpen(false);\n        router.push(path);\n    };\n    // 如果正在加载，显示加载状态\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            className: \"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse\",\n            disabled: true,\n            children: \"加载中...\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    // 如果未登录，显示登录按钮\n    if (!session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors\",\n                    onClick: ()=>setIsLoginModalOpen(true),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSignInAlt, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        \"登录\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoginModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: isLoginModalOpen,\n                    onClose: ()=>setIsLoginModalOpen(false)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 如果已登录，显示用户菜单\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\",\n                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\",\n                        children: session.user?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: session.user.image,\n                            alt: session.user.name || '',\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-gray-600\",\n                            children: session.user?.name?.charAt(0) || 'U'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm hidden md:block\",\n                        children: session.user?.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaChevronDown, {\n                        className: `text-xs transition-transform ${isMenuOpen ? 'rotate-180' : ''}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-10\",\n                        onClick: ()=>setIsMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\",\n                                            children: session.user?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: session.user.image,\n                                                alt: session.user.name || '',\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-medium text-gray-600\",\n                                                children: session.user?.name?.charAt(0) || 'U'\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm\",\n                                                    children: session.user?.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-xs\",\n                                                    children: session.user?.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this),\n                                                session.user?.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded\",\n                                                    children: \"管理员\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                        onClick: ()=>handleNavigation('/profile'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaUser, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"个人资料\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                        onClick: ()=>handleNavigation('/profile/submitted'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaList, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"我提交的工具\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                        onClick: ()=>handleNavigation('/profile/liked'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaHeart, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"我的收藏\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                        onClick: ()=>handleNavigation('/submit'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaPlus, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"提交工具\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this),\n                            session.user?.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t py-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                        onClick: ()=>handleNavigation('/admin'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"管理后台\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\",\n                                        onClick: ()=>handleNavigation('/settings'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaCog, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"设置\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\",\n                                        onClick: handleSignOut,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaChevronDown_FaCog_FaHeart_FaList_FaPlus_FaSignInAlt_FaSignOutAlt_FaUser_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSignOutAlt, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"退出登录\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/UserMenu.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FaBars_FaSearch_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaSearch,FaTimes!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _auth_UserMenu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../auth/UserMenu */ \"(ssr)/./src/components/auth/UserMenu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst NavLink = ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: href,\n        className: \"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined);\nconst Links = [\n    {\n        name: '首页',\n        href: '/'\n    },\n    {\n        name: '工具目录',\n        href: '/tools'\n    },\n    {\n        name: '分类',\n        href: '/categories'\n    },\n    {\n        name: '提交工具',\n        href: '/submit'\n    }\n];\nfunction Header() {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        const formData = new FormData(e.currentTarget);\n        const query = formData.get('search');\n        if (query.trim()) {\n            router.push(`/search?q=${encodeURIComponent(query.trim())}`);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"bg-white px-4 shadow-sm border-b border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-16 items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 hover:no-underline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-sm\",\n                                                children: \"AI\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: \"AI Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex space-x-4\",\n                                    children: Links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                            href: link.href,\n                                            children: link.name\n                                        }, link.name, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 max-w-md mx-8 hidden md:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaSearch_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                                className: \"text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            name: \"search\",\n                                            type: \"text\",\n                                            placeholder: \"搜索 AI 工具...\",\n                                            className: \"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_auth_UserMenu__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\",\n                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                    \"aria-label\": \"Open Menu\",\n                                    children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaSearch_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaTimes, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaSearch_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaBars, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 49\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-4\",\n                        children: [\n                            Links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavLink, {\n                                    href: link.href,\n                                    children: link.name\n                                }, link.name, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSearch,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaSearch_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_5__.FaSearch, {\n                                                    className: \"text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                name: \"search\",\n                                                type: \"text\",\n                                                placeholder: \"搜索 AI 工具...\",\n                                                className: \"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkU7QUFPOUQsU0FBU0EsZ0JBQWdCLEVBQUVFLFFBQVEsRUFBd0I7SUFDeEUscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBSZWFjdE5vZGUgfSBmcm9tICdyZWFjdCc7XG5cbmludGVyZmFjZSBTZXNzaW9uUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons","vendor-chunks/@babel","vendor-chunks/next-auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();